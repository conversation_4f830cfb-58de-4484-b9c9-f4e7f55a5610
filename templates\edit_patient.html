<!-- edit_patient.html -->
<!DOCTYPE html>
<html>
<head>
    <title>编辑孕产妇信息 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <h1>编辑孕产妇信息</h1>
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button">返回首页</a>
        <a href="{{ url_for('patient_records', patient_id=patient.id) }}" class="button">返回孕产妇记录</a>
    </div>

    <div class="compact-container">
        <form class="compact-form" action="{{ url_for('update_patient', patient_id=patient.id) }}" method="post">
            <!-- 基本信息 -->
            <div class="form-row">
                <div class="form-group">
                    <label for="name">姓名</label>
                    <input type="text" id="name" name="name" value="{{ patient.name }}" required>
                </div>
                <div class="form-group">
                    <label for="outpatient_number">门诊病历号</label>
                    <input type="text" id="outpatient_number" name="outpatient_number" value="{{ patient.outpatient_number }}" required>
                </div>
                <div class="form-group">
                    <label for="id_card">身份证号</label>
                    <input type="text" id="id_card" name="id_card" value="{{ patient.id_card }}" required>
                </div>
                <div class="form-group">
                    <label for="age">年龄</label>
                    <input type="number" id="age" name="age" value="{{ patient.age }}" required min="1">
                </div>
                <div class="form-group">
                    <label for="pregnancy_count">怀孕次数</label>
                    <input type="number" id="pregnancy_count" name="pregnancy_count" value="{{ patient.pregnancy_count }}" required min="1">
                </div>
                <div class="form-group">
                    <label for="delivery_count">分娩次数</label>
                    <input type="number" id="delivery_count" name="delivery_count" value="{{ patient.delivery_count }}" required min="0">
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-row">
                <div class="form-group">
                    <label for="phone">联系电话</label>
                    <input type="text" id="phone" name="phone" value="{{ patient.phone }}" required>
                </div>
                <div class="form-group">
                    <label for="address">现居住地</label>
                    <input type="text" id="address" name="address" value="{{ patient.address }}" required>
                </div>
                <div class="form-group">
                    <label for="husband_name">丈夫姓名</label>
                    <input type="text" id="husband_name" name="husband_name" value="{{ patient.husband_name }}">
                </div>
            </div>

            <!-- 就医信息 -->
            <div class="card mb-3">
                <div class="card-header">就医信息</div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="last_menstrual_date">末次月经</label>
                            <input type="date" id="last_menstrual_date" name="last_menstrual_date" 
                                   value="{{ patient.last_menstrual_date }}"
                                   onchange="calculatePregnancyWeekAndDueDate()">
                        </div>
                        <div class="form-group">
                            <label>当前孕周</label>
                            <input type="text" id="pregnancy_week" readonly 
                                   placeholder="自动计算" style="background-color: #f5f5f5;">
                        </div>
                        <div class="form-group">
                            <label for="due_date">预产期</label>
                            <input type="date" id="due_date" name="due_date" 
                                   value="{{ patient.due_date }}"
                                   {% if patient.has_given_birth == '在孕' %}readonly style="background-color: #f5f5f5;"{% endif %}>
                        </div>
                        <div class="form-group">
                            <label for="first_visit_date">初诊日期</label>
                            <input type="date" id="first_visit_date" name="first_visit_date" 
                                   value="{{ today }}" required>
                        </div>
                        <div class="form-group">
                            <label for="registration_date">建册日期</label>
                            <input type="date" id="registration_date" name="registration_date" 
                                   value="{{ today }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="is_high_risk">是否高危：</label>
                                <select class="form-control" id="is_high_risk" name="is_high_risk">
                                    <option value="false" {% if patient.is_high_risk == 'false' or patient.is_high_risk == '否' or not patient.is_high_risk %}selected{% endif %}>否</option>
                                    <option value="true" {% if patient.is_high_risk == 'true' or patient.is_high_risk == '是' or patient.is_high_risk == '高危' %}selected{% endif %}>高危</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="risk_factors">高危因素：</label>
                                <textarea class="form-control" id="risk_factors" name="risk_factors" rows="2">{{ patient.risk_factors or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 管理信息 -->
            <div class="form-row">
                <div class="form-group">
                    <label for="doctor_in_charge">主管医生</label>
                    <select id="doctor_in_charge" name="doctor_in_charge" required>
                        {% for doctor in doctor_history %}
                            <option value="{{ doctor.doctor_name }}" {{ 'selected' if doctor.doctor_name == patient.doctor_in_charge else '' }}>{{ doctor.doctor_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="management_org">管理机构</label>
                    <input type="text" id="management_org" name="management_org" value="涟源市妇幼保健院" required>
                </div>
                <div class="form-group">
                    <label for="is_vip">VIP状态</label>
                    <select id="is_vip" name="is_vip" required>
                        <option value="false" {% if not patient.is_vip %}selected{% endif %}>否</option>
                        <option value="true" {% if patient.is_vip %}selected{% endif %}>是</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="has_given_birth">分娩状态</label>
                    <select class="form-control" id="has_given_birth" name="has_given_birth" onchange="updatePregnancyWeek()">
                        <option value="在孕" {% if patient.has_given_birth == '在孕' %}selected{% endif %}>在孕</option>
                        <option value="已分娩" {% if patient.has_given_birth == '已分娩' %}selected{% endif %}>已分娩</option>
                        <option value="已流产" {% if patient.has_given_birth == '已流产' %}selected{% endif %}>已流产</option>
                        <option value="已引产" {% if patient.has_given_birth == '已引产' %}selected{% endif %}>已引产</option>
                    </select>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="button submit-btn">保存修改</button>
            </div>
        </form>
    </div>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
            {% for message in messages %}
                <div class="alert">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <script>
        function updatePregnancyWeek() {
            const birthStatus = document.getElementById('has_given_birth').value;
            const pregnancyWeekInput = document.getElementById('pregnancy_week');
            const dueDateInput = document.getElementById('due_date');
            
            if (birthStatus === '在孕') {
                // 如果有末次月经日期，重新计算孕周和预产期
                const lastMenstrualDate = document.getElementById('last_menstrual_date').value;
                if (lastMenstrualDate) {
                    calculatePregnancyWeekAndDueDate();
                }
                dueDateInput.readOnly = true;
                dueDateInput.style.backgroundColor = '#f5f5f5';
            } else {
                // 如果已分娩/流产/引产，直接显示状态
                pregnancyWeekInput.value = birthStatus;
                // 允许手动编辑预产期
                dueDateInput.readOnly = false;
                dueDateInput.style.backgroundColor = '';
            }
        }

        function calculatePregnancyWeekAndDueDate() {
            const birthStatus = document.getElementById('has_given_birth').value;
            const pregnancyWeekInput = document.getElementById('pregnancy_week');
            const dueDateInput = document.getElementById('due_date');
            const lastMenstrualDateInput = document.getElementById('last_menstrual_date').value;
            
            if (birthStatus !== '在孕') {
                pregnancyWeekInput.value = birthStatus;
                dueDateInput.readOnly = false;
                dueDateInput.style.backgroundColor = '';
                return;
            }

            if (!lastMenstrualDateInput) {
                pregnancyWeekInput.value = '';
                dueDateInput.value = '';
                return;
            }

            try {
                const lastMenstrualDate = new Date(lastMenstrualDateInput);
                const today = new Date();
                
                // 计算孕周
                const diffTime = Math.abs(today - lastMenstrualDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                const weeks = Math.floor(diffDays / 7);
                const days = diffDays % 7;
                
                pregnancyWeekInput.value = `${weeks}周${days}天`;
                
                // 计算预产期（末次月经 + 280天）
                const dueDate = new Date(lastMenstrualDate);
                dueDate.setDate(dueDate.getDate() + 280);
                
                // 格式化预产期日期为YYYY-MM-DD
                const year = dueDate.getFullYear();
                const month = String(dueDate.getMonth() + 1).padStart(2, '0');
                const day = String(dueDate.getDate()).padStart(2, '0');
                const dueDateFormatted = `${year}-${month}-${day}`;
                
                dueDateInput.value = dueDateFormatted;
                dueDateInput.readOnly = true;
                dueDateInput.style.backgroundColor = '#f5f5f5';
            } catch (error) {
                console.error('日期计算错误:', error);
                pregnancyWeekInput.value = '';
                dueDateInput.value = '';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 加载医生列表
            fetch('/get_doctors')
                .then(response => response.json())
                .then(doctors => {
                    const datalist = document.getElementById('doctor-list');
                    doctors.forEach(doctor => {
                        const option = document.createElement('option');
                        option.value = doctor.doctor_name;
                        datalist.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading doctors:', error));
                
            // 页面加载时如果已有末次月经日期则计算
            const lastMenstrualDate = document.getElementById('last_menstrual_date').value;
            if (lastMenstrualDate) {
                calculatePregnancyWeekAndDueDate();
            }

            updatePregnancyWeek();
        });

        // 确保在末次月经日期变化时触发计算
        document.getElementById('last_menstrual_date').addEventListener('change', function() {
            calculatePregnancyWeekAndDueDate();
        });
    </script>
</body>
</html>
