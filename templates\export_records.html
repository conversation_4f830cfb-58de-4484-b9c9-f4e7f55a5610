<!DOCTYPE html>
<html>
<head>
    <title>导出记录 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .export-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input[type="date"] {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .button-group {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        .export-button {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .export-all-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .alert {
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .pregnancy-weeks-range {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .pregnancy-weeks-range input {
            width: 100px;
        }
    </style>
</head>
<body>
    <h1>导出产检记录</h1>
    <p class="subtitle" style="text-align: center;">导出的日期范围为产检记录日期</p>
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button">返回首页</a>
    </div>

    <div class="export-container">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('export_records') }}">
            <div class="form-group">
                <label for="start_date">开始日期：</label>
                <input type="date" id="start_date" name="start_date" required>
            </div>
            
            <div class="form-group">
                <label for="end_date">结束日期：</label>
                <input type="date" id="end_date" name="end_date" required>
            </div>
            
            <div class="form-group">
                <label for="pregnancy_weeks">孕周范围：</label>
                <div class="pregnancy-weeks-range">
                    <input type="number" id="min_weeks" name="min_weeks" min="0" max="45" placeholder="最小周数">
                    <span>至</span>
                    <input type="number" id="max_weeks" name="max_weeks" min="0" max="45" placeholder="最大周数">
                    <span>周</span>
                </div>
            </div>
            
            <div class="button-group">
                <button type="submit" class="export-button">导出选定范围记录</button>
                <button type="submit" class="export-all-button" onclick="clearDates(event)">导出全部记录</button>
            </div>
        </form>
    </div>

    <script>
        // 设置日期输入框的默认值为当前日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('end_date').value = today;
            
            // 设置默认开始日期为30天前
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            document.getElementById('start_date').value = thirtyDaysAgo.toISOString().split('T')[0];
        });
        
        // 清除日期并提交表单以导出全部记录
        function clearDates(event) {
            event.preventDefault();
            document.getElementById('start_date').value = '';
            document.getElementById('end_date').value = '';
            document.querySelector('form').submit();
        }
        
        // 验证日期范围
        document.querySelector('form').addEventListener('submit', function(event) {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            
            if (startDate && endDate && startDate > endDate) {
                event.preventDefault();
                alert('开始日期不能晚于结束日期');
            }
        });
    </script>
</body>
</html> 