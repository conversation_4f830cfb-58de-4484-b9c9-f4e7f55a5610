# 快速启动指南

## 🚀 5分钟快速启动

### 1. 环境检查
```bash
# 检查Python版本（需要3.8+）
python --version

# 检查MySQL服务状态
# Windows: 在服务管理器中检查MySQL服务
# Linux: sudo systemctl status mysql
```

### 2. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt
```

### 3. 配置数据库
编辑 `config.py` 文件，修改数据库连接信息：
```python
connection = mysql.connector.connect(
    host='你的数据库主机',      # 例如: 'localhost'
    user='你的数据库用户名',    # 例如: 'root'
    password='你的数据库密码',  # 你的密码
    port=3306,
    database='pregnancy_management',
    # ...
)
```

### 4. 初始化数据库
```sql
-- 在MySQL中执行
CREATE DATABASE pregnancy_management;
USE pregnancy_management;
SOURCE create_tables.sql;
```

### 5. 启动应用
```bash
# 方式1: 使用启动脚本（推荐）
python run.py

# 方式2: 直接运行
python app.py
```

### 6. 访问系统
打开浏览器访问: http://127.0.0.1:5000

## 📋 首次使用步骤

1. **注册账号**: 点击"注册"创建新用户
2. **登录系统**: 使用注册的账号登录
3. **添加孕产妇**: 点击"添加孕产妇信息"
4. **录入产检**: 为孕产妇添加产检记录
5. **查看管理**: 使用搜索和管理功能

## 🔧 常见问题解决

### 数据库连接失败
```bash
# 检查MySQL服务
# Windows:
net start mysql

# Linux:
sudo systemctl start mysql

# 检查数据库是否存在
mysql -u root -p -e "SHOW DATABASES;"
```

### 端口被占用
```bash
# 修改端口（在run.py中）
export PORT=8000
python run.py

# 或者直接指定
python run.py --port 8000
```

### 权限问题
```bash
# 确保uploads目录有写权限
chmod 755 uploads/

# Windows中右键uploads文件夹 -> 属性 -> 安全 -> 编辑权限
```

## 🧪 测试系统
```bash
# 运行测试脚本
python test_app.py

# 应该看到类似输出:
# ✓ 文件结构完整
# ✓ 模板文件完整
# ✓ 所有模块导入成功
# ...
```

## 📁 重要文件说明

- `app.py` - 主应用程序
- `config.py` - 数据库配置
- `run.py` - 启动脚本
- `requirements.txt` - 依赖包列表
- `templates/` - HTML模板文件
- `static/` - 静态文件（CSS、模板等）
- `uploads/` - 文件上传目录

## 🔒 默认配置

- **端口**: 5000
- **调试模式**: 开启（开发环境）
- **上传限制**: 16MB
- **支持格式**: .xlsx, .xls

## 📞 获取帮助

如果遇到问题：
1. 查看 `README.md` 详细文档
2. 查看 `DEPLOYMENT.md` 部署指南
3. 运行 `python test_app.py` 检查系统状态
4. 查看 `app.log` 日志文件

## 🎯 下一步

系统启动成功后，建议：
1. 阅读完整的 `README.md` 文档
2. 了解所有功能模块
3. 配置生产环境（参考 `DEPLOYMENT.md`）
4. 定期备份数据库

---
**祝您使用愉快！** 🎉
