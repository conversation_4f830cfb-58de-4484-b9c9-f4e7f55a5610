USE pregnancy_management;

-- 添加怀孕次数列到patient_info表
ALTER TABLE patient_info ADD COLUMN pregnancy_count INT DEFAULT 1;

-- 添加分娩次数列到patient_info表
ALTER TABLE patient_info ADD COLUMN delivery_count INT DEFAULT 0;

-- 创建主管医生历史记录表
CREATE TABLE IF NOT EXISTS doctor_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    doctor_name VARCHAR(50) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加门诊号字段到产检记录表
ALTER TABLE checkup_records ADD COLUMN outpatient_number VARCHAR(50);

-- 从patient_info表更新已有记录的门诊号
UPDATE checkup_records cr
JOIN patient_info pi ON cr.patient_id = pi.id
SET cr.outpatient_number = pi.outpatient_number;

-- 添加索引以提高查询性能
CREATE INDEX idx_outpatient_number ON checkup_records(outpatient_number); 