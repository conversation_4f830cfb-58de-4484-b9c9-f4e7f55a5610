#!/bin/bash
# 应用部署脚本
# 在服务器上运行此脚本来部署应用

set -e

APP_DIR="/var/www/pregnancy_management"
CURRENT_DIR=$(pwd)

echo "🚀 部署孕产妇管理系统应用"
echo "=========================="

# 检查是否在正确的目录
if [ ! -f "app.py" ]; then
    echo "❌ 错误: 请在包含app.py的目录中运行此脚本"
    exit 1
fi

# 1. 检查当前目录
if [ "$PWD" = "$APP_DIR" ]; then
    echo "📁 已在应用目录中，跳过文件复制..."
else
    echo "📁 复制应用文件..."
    sudo cp -r . $APP_DIR/
    cd $APP_DIR
fi

# 2. 设置文件权限
echo "🔒 设置文件权限..."
sudo chown -R www-data:www-data $APP_DIR

# 3. 创建虚拟环境
echo "🐍 创建Python虚拟环境..."
if [ ! -d "venv" ]; then
    sudo -u www-data python3 -m venv venv
fi

# 4. 安装依赖
echo "📦 安装Python依赖..."
sudo -u www-data venv/bin/pip install --upgrade pip
sudo -u www-data venv/bin/pip install -r requirements.txt

# 5. 创建必要目录
echo "📁 创建必要目录..."
sudo -u www-data mkdir -p uploads
sudo -u www-data mkdir -p logs
sudo chmod 755 uploads
sudo chmod 755 logs

# 6. 导入数据库表
echo "🗄️ 导入数据库表..."
if [ -f "create_tables.sql" ]; then
    mysql -u root -pwindows1 pregnancy_management < create_tables.sql
    echo "✅ 数据库表导入成功"
else
    echo "⚠️ 警告: 未找到create_tables.sql文件"
fi

# 7. 创建Gunicorn配置文件
echo "⚙️ 创建Gunicorn配置..."
sudo -u www-data tee gunicorn_config.py > /dev/null << 'EOF'
import multiprocessing

# 服务器配置
bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 进程管理
preload_app = True
daemon = False
pidfile = "/tmp/pregnancy_management.pid"

# 日志配置
accesslog = "/var/log/pregnancy_management/access.log"
errorlog = "/var/log/pregnancy_management/error.log"
loglevel = "info"

# 超时配置
timeout = 30
keepalive = 2
EOF

# 8. 测试应用
echo "🧪 测试应用..."
sudo -u www-data venv/bin/python -c "
try:
    import app
    print('✅ 应用导入成功')
except Exception as e:
    print(f'❌ 应用导入失败: {e}')
    exit(1)
"

# 9. 启动服务
echo "🚀 启动应用服务..."
sudo systemctl start pregnancy-management
sudo systemctl enable pregnancy-management

# 等待服务启动
sleep 3

# 检查服务状态
if sudo systemctl is-active --quiet pregnancy-management; then
    echo "✅ 应用服务启动成功"
else
    echo "❌ 应用服务启动失败"
    sudo systemctl status pregnancy-management
    exit 1
fi

# 10. 重启Nginx
echo "🌐 重启Nginx..."
sudo systemctl restart nginx

# 11. 测试Web服务
echo "🧪 测试Web服务..."
sleep 2
if curl -s http://127.0.0.1:8000 > /dev/null; then
    echo "✅ Web服务响应正常"
else
    echo "❌ Web服务无响应"
fi

echo ""
echo "🎉 应用部署完成！"
echo "=================="
echo "📍 内部地址: http://127.0.0.1:8000"
echo "🌐 外部地址: http://pm.beimoyinhenlinlin.cn/"
echo ""
echo "📋 后续操作："
echo "1. 配置SSL证书:"
echo "   sudo certbot --nginx -d pm.beimoyinhenlinlin.cn"
echo ""
echo "2. 查看服务状态:"
echo "   sudo systemctl status pregnancy-management"
echo ""
echo "3. 查看日志:"
echo "   sudo journalctl -u pregnancy-management -f"
echo "   tail -f /var/log/pregnancy_management/error.log"
echo ""
echo "4. 重启服务:"
echo "   sudo systemctl restart pregnancy-management"

# 返回原目录
cd $CURRENT_DIR
