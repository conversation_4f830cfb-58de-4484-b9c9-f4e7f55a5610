2025-01-22 23:23:41,594 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:52:23,159 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:54:38,488 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:55:46,174 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:55:46,461 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-22 23:55:46,462 - mysql.connector - INFO - plugin_name: caching_sha2_password
2025-01-22 23:55:46,462 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-01-22 23:55:46,545 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-22 23:55:46,545 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-01-22 23:55:46,547 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-01-22 23:55:46,759 - config - INFO - 数据库连接成功！
2025-01-22 23:55:47,018 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-01-22 23:55:47,018 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-01-22 23:55:47,021 - werkzeug - INFO -  * Restarting with stat
2025-01-22 23:55:47,934 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:55:48,182 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-22 23:55:48,182 - mysql.connector - INFO - plugin_name: caching_sha2_password
2025-01-22 23:55:48,182 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-01-22 23:55:48,254 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-22 23:55:48,255 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-01-22 23:55:48,256 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-01-22 23:55:48,431 - config - INFO - 数据库连接成功！
2025-01-22 23:55:48,628 - werkzeug - WARNING -  * Debugger is active!
2025-01-22 23:55:48,641 - werkzeug - INFO -  * Debugger PIN: 892-553-922
2025-01-22 23:56:46,325 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:56:46] "GET / HTTP/1.1" 200 -
2025-01-22 23:56:46,480 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:56:46] "GET /static/style.css HTTP/1.1" 200 -
2025-01-22 23:56:47,935 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:56:48,445 - config - INFO - 数据库连接成功！
2025-01-22 23:56:48,575 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:56:48] "GET /appointments HTTP/1.1" 200 -
2025-01-22 23:56:48,593 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:56:48] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-22 23:59:09,183 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:09] "GET / HTTP/1.1" 200 -
2025-01-22 23:59:09,200 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:09] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-22 23:59:10,246 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:10] "GET /search_patient HTTP/1.1" 200 -
2025-01-22 23:59:10,260 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-22 23:59:14,260 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:59:14,725 - config - INFO - 数据库连接成功！
2025-01-22 23:59:14,869 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:14] "POST /search_patient HTTP/1.1" 200 -
2025-01-22 23:59:14,878 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-22 23:59:16,834 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:59:17,320 - config - INFO - 数据库连接成功！
2025-01-22 23:59:17,529 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:17] "GET /patient_records/161 HTTP/1.1" 200 -
2025-01-22 23:59:17,539 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:17] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-22 23:59:43,029 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-22 23:59:43,500 - config - INFO - 数据库连接成功！
2025-01-22 23:59:43,657 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:43] "GET /checkup_detail/3 HTTP/1.1" 200 -
2025-01-22 23:59:43,675 - werkzeug - INFO - 127.0.0.1 - - [22/Jan/2025 23:59:43] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 00:00:14,886 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 00:00:15,406 - config - INFO - 数据库连接成功！
2025-01-23 00:00:15,613 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 00:00:15] "GET /patient_records/161 HTTP/1.1" 200 -
2025-01-23 00:00:15,637 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 00:00:15] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 00:00:22,294 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 00:00:22] "GET / HTTP/1.1" 200 -
2025-01-23 00:00:22,313 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 00:00:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:02:22,134 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 01:02:22,636 - config - INFO - 数据库连接成功！
2025-01-23 01:02:22,760 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:22] "GET /appointments HTTP/1.1" 200 -
2025-01-23 01:02:22,776 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:02:40,419 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:40] "GET / HTTP/1.1" 200 -
2025-01-23 01:02:40,432 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:02:41,722 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:41] "GET /add_patient HTTP/1.1" 200 -
2025-01-23 01:02:41,737 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:41] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:02:55,889 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:55] "GET / HTTP/1.1" 200 -
2025-01-23 01:02:55,906 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:02:58,198 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 01:02:58,654 - config - INFO - 数据库连接成功！
2025-01-23 01:02:58,769 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:58] "GET /appointments HTTP/1.1" 200 -
2025-01-23 01:02:58,782 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:02:58] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:03:00,959 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 01:03:01,428 - config - INFO - 数据库连接成功！
2025-01-23 01:03:01,622 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:03:01] "GET /add_checkup/161 HTTP/1.1" 200 -
2025-01-23 01:03:01,633 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:03:01] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:27,190 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 01:17:27,773 - config - INFO - 数据库连接成功！
2025-01-23 01:17:27,967 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:27] "GET /patient_records/161 HTTP/1.1" 200 -
2025-01-23 01:17:27,981 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:27] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:35,498 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:35] "GET /search_patient HTTP/1.1" 200 -
2025-01-23 01:17:35,510 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:35] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:37,448 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:37] "GET / HTTP/1.1" 200 -
2025-01-23 01:17:37,462 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:37] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:38,641 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:38] "GET /search_patient HTTP/1.1" 200 -
2025-01-23 01:17:38,657 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:38] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:40,134 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:40] "GET / HTTP/1.1" 200 -
2025-01-23 01:17:40,149 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:41,201 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:41] "GET /search_patient HTTP/1.1" 200 -
2025-01-23 01:17:41,214 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:41] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:41,863 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:41] "GET /add_patient HTTP/1.1" 200 -
2025-01-23 01:17:41,875 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:41] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 01:17:45,453 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:45] "GET / HTTP/1.1" 200 -
2025-01-23 01:17:45,465 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 01:17:45] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 21:58:22,816 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 21:58:23,143 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-23 21:58:23,143 - mysql.connector - INFO - plugin_name: caching_sha2_password
2025-01-23 21:58:23,143 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-01-23 21:58:23,225 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-23 21:58:23,225 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-01-23 21:58:23,227 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-01-23 21:58:23,433 - config - INFO - 数据库连接成功！
2025-01-23 21:58:23,679 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-01-23 21:58:23,679 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-01-23 21:58:23,681 - werkzeug - INFO -  * Restarting with stat
2025-01-23 21:58:24,376 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 21:58:24,609 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-23 21:58:24,609 - mysql.connector - INFO - plugin_name: caching_sha2_password
2025-01-23 21:58:24,609 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-01-23 21:58:24,685 - mysql.connector - INFO - package: mysql.connector.plugins
2025-01-23 21:58:24,686 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-01-23 21:58:24,687 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-01-23 21:58:24,878 - config - INFO - 数据库连接成功！
2025-01-23 21:58:25,089 - werkzeug - WARNING -  * Debugger is active!
2025-01-23 21:58:25,100 - werkzeug - INFO -  * Debugger PIN: 892-553-922
2025-01-23 21:58:27,904 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 21:58:27] "GET / HTTP/1.1" 200 -
2025-01-23 21:58:28,004 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 21:58:28] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 21:58:28,058 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 21:58:28] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-01-23 22:03:19,092 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 22:03:19,519 - config - INFO - 数据库连接成功！
2025-01-23 22:03:19,631 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:03:19] "GET /appointments HTTP/1.1" 200 -
2025-01-23 22:03:19,643 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:03:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 22:03:59,072 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:03:59] "GET / HTTP/1.1" 200 -
2025-01-23 22:03:59,083 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:03:59] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 22:05:35,883 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:05:35] "GET /search_patient HTTP/1.1" 200 -
2025-01-23 22:05:35,894 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:05:35] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 22:05:39,474 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 22:05:39,962 - config - INFO - 数据库连接成功！
2025-01-23 22:05:40,123 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:05:40] "POST /search_patient HTTP/1.1" 200 -
2025-01-23 22:05:40,130 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:05:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 22:05:40,998 - config - INFO - 尝试连接数据库 (尝试 1/3)
2025-01-23 22:05:41,476 - config - INFO - 数据库连接成功！
2025-01-23 22:05:41,678 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:05:41] "GET /patient_records/161 HTTP/1.1" 200 -
2025-01-23 22:05:41,687 - werkzeug - INFO - 127.0.0.1 - - [23/Jan/2025 22:05:41] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-01-23 22:43:55,541 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\测试\\app.py', reloading
2025-01-23 22:43:55,602 - werkzeug - INFO -  * Restarting with stat
