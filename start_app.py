"""
简化的应用启动脚本
解决numpy导入问题
"""
import sys
import os

def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'flask': 'Flask',
        'mysql.connector': 'mysql-connector-python', 
        'pandas': 'pandas',
        'openpyxl': 'openpyxl',
        'werkzeug': 'werkzeug'
    }
    
    missing = []
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            print(f"✓ {pip_name}")
        except ImportError:
            missing.append(pip_name)
            print(f"✗ {pip_name} - 缺失")
    
    if missing:
        print(f"\n请安装缺失的包:")
        print(f"pip install {' '.join(missing)}")
        return False
    return True

def main():
    print("=" * 50)
    print("🏥 孕产妇管理系统启动检查")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查依赖
    print("\n检查依赖包:")
    if not check_dependencies():
        return 1
    
    print("\n✅ 所有依赖检查通过!")
    
    try:
        # 导入应用
        print("\n正在导入应用...")
        from app import app
        
        print("✅ 应用导入成功!")
        
        # 启动应用
        print("\n🚀 启动应用...")
        print("📍 访问地址: http://127.0.0.1:5000")
        print("🔧 调试模式: 开启")
        print("\n按 Ctrl+C 停止服务器\n")
        
        app.run(host='127.0.0.1', port=5000, debug=True)
        
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print("\n可能的解决方案:")
        print("1. 重新安装pandas和numpy:")
        print("   pip uninstall pandas numpy -y")
        print("   pip install pandas numpy")
        print("2. 检查是否在正确的目录中运行")
        print("3. 尝试使用虚拟环境")
        return 1
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
