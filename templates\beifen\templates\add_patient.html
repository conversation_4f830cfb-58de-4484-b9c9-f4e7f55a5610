<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加孕产妇</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        window.onload = function() {
            {% if error %}
                alert("{{ error|escape }}");
            {% endif %}
        };
    </script>
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button secondary">
            <i class="fas fa-home"></i> 返回首页
        </a>
        <a href="{{ url_for('search_patient') }}" class="button secondary">
            <i class="fas fa-search"></i> 搜索孕产妇
        </a>
    </div>

    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1><i class="fas fa-user-plus"></i> 添加新孕产妇</h1>
            <p class="subtitle">请填写孕产妇的基本信息和就医信息</p>
        </div>

        <div class="form-container">
            <form method="post" class="patient-form">
                <!-- 基本信息 -->
                <div class="card">
                    <div class="form-section">
                        <h2><i class="fas fa-user"></i> 基本信息</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="name">姓名</label>
                                <input type="text" id="name" name="name" required 
                                       placeholder="请输入孕产妇姓名">
                            </div>
                            <div class="form-group">
                                <label for="age">年龄</label>
                                <input type="number" id="age" name="age" required 
                                       placeholder="请输入年龄" min="1" max="100" style="font-family: Arial; font-size: 16px;">
                            </div>
                            <div class="form-group">
                                <label for="id_card">身份证号</label>
                                <input type="text" id="id_card" name="id_card" required 
                                       placeholder="请输入18位身份证号" >
                                       <!-- pattern: 使用正则表达式来验证输入框内容的格式。这里使用的是\d{17}[\dXx]，其含义是：
                                       \d{17}：匹配17位数字。
                                       [\dXx]：匹配最后一个字符，可以是数字或大写的X或小写的x。 -->
                            </div>
                            <div class="form-group">
                                <label for="outpatient_number">门诊病历号</label>
                                <input type="text" id="outpatient_number" name="outpatient_number" required 
                                       placeholder="请输入门诊病历号">
                            </div>
                           
                        </div>
                    </div>
                </div>

                <!-- 联系信息 -->
                <div class="card">
                    <div class="form-section">
                        <h2><i class="fas fa-address-book"></i> 联系信息</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="phone">联系电话</label>
                                <input type="tel" id="phone" name="phone" required 
                                       placeholder="请输入手机号码" pattern="[0-9]{11}">
                            </div>
                            <div class="form-group">
                                <label for="address">现居住地</label>
                                <input type="text" id="address" name="address" required 
                                       placeholder="请输入详细地址">
                            </div>
                            <div class="form-group">
                                <label for="husband_name">丈夫姓名</label>
                                <input type="text" id="husband_name" name="husband_name" 
                                       placeholder="请输入丈夫姓名（选填）">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 就医信息 -->
                <div class="card">
                    <div class="form-section">
                        <h2><i class="fas fa-calendar-alt"></i> 就医信息</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="last_menstrual_date">末次月经</label>
                                <input type="date" id="last_menstrual_date" name="last_menstrual_date" required 
                                       onchange="calculatePregnancyWeekAndDueDate()">
                            </div>
                            <div class="form-group">
                                <label for="pregnancy_count">怀孕次数</label>
                                <input type="number" id="pregnancy_count" name="pregnancy_count" required min="1" value="1">
                            </div>
                            <div class="form-group">
                                <label for="delivery_count">分娩次数</label>
                                <input type="number" id="delivery_count" name="delivery_count" required min="0" value="0">
                            </div>
                            <div class="form-group">
                                <label>当前孕周</label>
                                <input type="text" id="pregnancy_week" readonly 
                                       placeholder="自动计算" style="background-color: #f5f5f5;">
                            </div>
                            <div class="form-group">
                                <label for="due_date">预产期</label>
                                <input type="date" id="due_date" name="due_date" 
                                       {% if not has_given_birth or has_given_birth == '在孕' %}
                                       readonly style="background-color: #f5f5f5;"
                                       {% endif %}>
                            </div>
                            <div class="form-group">
                                <label for="first_visit_date">初诊日期</label>
                                <input type="date" id="first_visit_date" name="first_visit_date" required>
                            </div>
                            <div class="form-group">
                                <label for="registration_date">建册日期</label>
                                <input type="date" id="registration_date" name="registration_date" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_high_risk">是否高危：</label>
                                        <select class="form-control" id="is_high_risk" name="is_high_risk">
                                            <option value="false">否</option>
                                            <option value="true">是</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group risk-factors-group">
                                        <label for="risk_factors">高危因素：</label>
                                        <textarea class="form-control" id="risk_factors" name="risk_factors" rows="2" 
                                                  placeholder="请输入高危因素（如有）"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 管理信息 -->
                <div class="card">
                    <div class="form-section">
                        <h2><i class="fas fa-user-md"></i> 管理信息</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="doctor_in_charge">主管医生</label>
                                <div class="doctor-input-group">
                                    <input type="text" id="doctor_in_charge" name="doctor_in_charge" required 
                                           list="doctor-list" autocomplete="off">
                                    <datalist id="doctor-list"></datalist>

                                </div>
                            </div>
                            <div class="form-group">
                                <label for="management_org">管理机构</label>
                                <input type="text" id="management_org" name="management_org" value="涟源市妇幼保健院" readonly>
                            </div>
                            <div class="form-group">
                                <label for="is_vip">VIP状态</label>
                                <select id="is_vip" name="is_vip" required>
                                    <option value="false">否</option>
                                    <option value="true">是</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="has_given_birth">分娩状态</label>
                                <select class="form-control" id="has_given_birth" name="has_given_birth" onchange="updatePregnancyWeek()">
                                    <option value="在孕">在孕</option>
                                    <option value="已分娩">已分娩</option>
                                    <option value="已流产">已流产</option>
                                    <option value="已引产">已引产</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="button">
                        <i class="fas fa-save"></i> 保存孕产妇信息
                    </button>
                </div>
            </form>
        </div>

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert {{ category }}">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <style>
        .form-actions {
            margin-top: calc(var(--spacing-unit) * 4);
            text-align: center;
        }
        
        .form-actions .button {
            padding: calc(var(--spacing-unit) * 2) calc(var(--spacing-unit) * 4);
            font-size: 1.1rem;
        }
        
        .form-section {
            padding: calc(var(--spacing-unit) * 3) 0;
            border-bottom: 2px solid #1e88e5;
        }
        
        .form-section:first-child {
            padding-top: 0;
        }
        
        .form-section:last-child {
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .card {
            margin-bottom: calc(var(--spacing-unit) * 3);
        }
        
        .card:last-child {
            margin-bottom: 0;
        }
        
        .doctor-input-group {
            display: flex;
            gap: var(--spacing-unit);
            align-items: center;
        }
        
        .doctor-input-group input {
            flex: 1;
        }
        
        .doctor-input-group .button {
            padding: calc(var(--spacing-unit) * 1.5);
            height: 100%;
        }
        
        @media (max-width: 768px) {
            .form-actions {
                margin-top: calc(var(--spacing-unit) * 3);
            }
            
            .form-actions .button {
                width: 100%;
            }
        }

        .risk-factors-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            width: 100%;
        }

        .risk-factors-group label {
            margin-bottom: 10px;
        }

        .risk-factors-group textarea {
            width: 90%;
            min-height: 60px;
            text-align: center;
        }

        .risk-factors-group textarea::placeholder {
            text-align: center;
            padding-top: 15px;
        }
    </style>

    <script>
        function updatePregnancyWeek() {
            const birthStatus = document.getElementById('has_given_birth').value;
            const pregnancyWeekInput = document.getElementById('pregnancy_week');
            const dueDateInput = document.getElementById('due_date');
            
            if (birthStatus === '在孕') {
                // 如果有末次月经日期，重新计算孕周和预产期
                const lastMenstrualDate = document.getElementById('last_menstrual_date').value;
                if (lastMenstrualDate) {
                    calculatePregnancyWeekAndDueDate();
                }
                dueDateInput.readOnly = true;
                dueDateInput.style.backgroundColor = '#f5f5f5';
            } else {
                // 如果已分娩/流产/引产，直接显示状态
                pregnancyWeekInput.value = birthStatus;
                // 允许手动编辑预产期
                dueDateInput.readOnly = false;
                dueDateInput.style.backgroundColor = '';
            }
        }

        function calculatePregnancyWeekAndDueDate() {
            const birthStatus = document.getElementById('has_given_birth').value;
            const pregnancyWeekInput = document.getElementById('pregnancy_week');
            const dueDateInput = document.getElementById('due_date');
            const lastMenstrualDateInput = document.getElementById('last_menstrual_date').value;
            
            if (birthStatus !== '在孕') {
                pregnancyWeekInput.value = birthStatus;
                dueDateInput.readOnly = false;
                dueDateInput.style.backgroundColor = '';
                return;
            }

            if (!lastMenstrualDateInput) {
                pregnancyWeekInput.value = '';
                dueDateInput.value = '';
                return;
            }

            try {
                const lastMenstrualDate = new Date(lastMenstrualDateInput);
                const today = new Date();
                
                // 计算孕周
                const diffTime = Math.abs(today - lastMenstrualDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                const weeks = Math.floor(diffDays / 7);
                const days = diffDays % 7;
                
                pregnancyWeekInput.value = `${weeks}周${days}天`;
                
                // 计算预产期（末次月经 + 280天）
                const dueDate = new Date(lastMenstrualDate);
                dueDate.setDate(dueDate.getDate() + 280);
                
                // 格式化预产期日期为YYYY-MM-DD
                const year = dueDate.getFullYear();
                const month = String(dueDate.getMonth() + 1).padStart(2, '0');
                const day = String(dueDate.getDate()).padStart(2, '0');
                const dueDateFormatted = `${year}-${month}-${day}`;
                
                dueDateInput.value = dueDateFormatted;
                dueDateInput.readOnly = true;
                dueDateInput.style.backgroundColor = '#f5f5f5';
            } catch (error) {
                console.error('日期计算错误:', error);
                pregnancyWeekInput.value = '';
                dueDateInput.value = '';
            }
        }

        // 确保在末次月经日期变化时触发计算
        document.getElementById('last_menstrual_date').addEventListener('change', function() {
            calculatePregnancyWeekAndDueDate();
        });

        // 页面加载时设置默认日期和初始化其他功能
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前日期
            const today = new Date();
            const todayFormatted = today.toISOString().split('T')[0];
            
            // 设置初诊日期和建册日期的默认值为当天
            document.getElementById('first_visit_date').value = todayFormatted;
            document.getElementById('registration_date').value = todayFormatted;

            // 如果已有末次月经日期则计算孕周和预产期
            const lastMenstrualDate = document.getElementById('last_menstrual_date').value;
            if (lastMenstrualDate) {
                calculatePregnancyWeekAndDueDate();
            }

            // 获取主管医生历史记录
            fetch('/get_doctors')
                .then(response => response.json())
                .then(doctors => {
                    const datalist = document.getElementById('doctor-list');
                    doctors.forEach(doctorName => {
                        const option = document.createElement('option');
                        option.value = doctorName;
                        datalist.appendChild(option);
                    });
                })
                .catch(error => console.error('获取医生列表失败:', error));

            updatePregnancyWeek();
        });
    </script>
</body>
</html> 