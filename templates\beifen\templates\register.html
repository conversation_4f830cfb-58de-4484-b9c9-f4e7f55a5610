<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>涟源市妇幼保健院孕妇产检信息管理系统 - 注册</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f0f8ff;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .login-container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 300px;
            text-align: center;
        }
        h1 {
            color: #003366;
            margin-bottom: 20px;
        }
        form input {
            width: calc(100% - 22px);
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #cccccc;
            border-radius: 4px;
            font-size: 16px;
        }
        form button {
            width: 100%;
            padding: 10px;
            background-color: #007bff;
            color: #ffffff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        form button:hover {
            background-color: #0056b3;
        }
        .error-messages {
            list-style-type: none;
            padding: 0;
            margin: 10px 0 20px 0;
        }
        .error-messages li {
            color: #ff0000;
            margin: 5px 0;
        }
        a {
            color: #007bff;
            text-decoration: none;
            font-size: 16px;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>注册</h1>
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <ul class="error-messages">
                    {% for message in messages %}
                        <li>{{ message }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        <form action="{{ url_for('register') }}" method="post">
            <input type="text" name="username" placeholder="用户名" required>
            <input type="password" name="password" placeholder="密码" required>
            <input type="password" name="confirm_password" placeholder="确认密码" required>
            <button type="submit">注册</button>
        </form>
        <a href="{{ url_for('login') }}">已有账号？登录</a>
    </div>
</body>
</html>
