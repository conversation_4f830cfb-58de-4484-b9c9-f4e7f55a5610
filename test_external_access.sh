#!/bin/bash
# 测试外部访问配置

echo "🧪 测试外部访问配置"
echo "==================="

# 1. 检查应用服务状态
echo "📊 检查应用服务状态..."
if sudo systemctl is-active --quiet pregnancy-management; then
    echo "✅ pregnancy-management服务运行正常"
else
    echo "❌ pregnancy-management服务未运行"
    sudo systemctl status pregnancy-management
    exit 1
fi

# 2. 检查Nginx状态
echo "🌐 检查Nginx状态..."
if command -v nginx > /dev/null; then
    if sudo systemctl is-active --quiet nginx; then
        echo "✅ Nginx服务运行正常"
    else
        echo "❌ Nginx服务未运行"
        sudo systemctl status nginx
        exit 1
    fi
else
    echo "❌ Nginx未安装"
    echo "请运行: sudo apt install nginx"
    exit 1
fi

# 3. 检查端口监听
echo "🔌 检查端口监听状态..."
if sudo netstat -tlnp | grep :8000 > /dev/null; then
    echo "✅ 端口8000正在监听 (应用服务)"
else
    echo "❌ 端口8000未监听"
fi

if sudo netstat -tlnp | grep :80 > /dev/null; then
    echo "✅ 端口80正在监听 (Nginx)"
else
    echo "❌ 端口80未监听"
fi

# 4. 测试内部访问
echo "🔗 测试内部访问..."
if curl -s http://127.0.0.1:8000 > /dev/null; then
    echo "✅ 内部应用访问正常"
else
    echo "❌ 内部应用访问失败"
fi

if curl -s http://127.0.0.1/ > /dev/null; then
    echo "✅ 内部Nginx访问正常"
else
    echo "❌ 内部Nginx访问失败"
fi

# 5. 测试外部IP访问
echo "🌍 测试外部IP访问..."
if curl -s http://*************/ > /dev/null; then
    echo "✅ 外部IP访问正常"
    echo "🎉 可以通过 http://*************/ 访问系统"
else
    echo "❌ 外部IP访问失败"
    echo "可能的原因:"
    echo "1. 防火墙阻止了80端口"
    echo "2. Nginx配置错误"
    echo "3. 网络连接问题"
fi

# 6. 测试健康检查
echo "💓 测试健康检查..."
if curl -s http://*************/health | grep -q "healthy"; then
    echo "✅ 健康检查正常"
else
    echo "❌ 健康检查失败"
fi

# 7. 检查防火墙状态
echo "🔒 检查防火墙状态..."
if sudo ufw status | grep -q "Status: active"; then
    echo "🔥 防火墙已启用"
    if sudo ufw status | grep -q "80"; then
        echo "✅ 端口80已开放"
    else
        echo "❌ 端口80未开放"
        echo "请运行: sudo ufw allow 80"
    fi
else
    echo "⚠️ 防火墙未启用"
fi

# 8. 显示访问信息
echo ""
echo "🎯 访问信息"
echo "==========="
echo "内部访问: http://127.0.0.1:8000"
echo "外部访问: http://*************/"
echo "健康检查: http://*************/health"
echo ""

# 9. 显示管理命令
echo "📋 管理命令"
echo "==========="
echo "查看应用日志: sudo journalctl -u pregnancy-management -f"
echo "查看Nginx日志: sudo tail -f /var/log/nginx/access.log"
echo "重启应用: sudo systemctl restart pregnancy-management"
echo "重启Nginx: sudo systemctl restart nginx"
echo "检查端口: sudo netstat -tlnp | grep -E ':(80|8000)'"
