#!/usr/bin/env python3
"""
简单的应用测试脚本
"""
import sys
import os

def test_imports():
    """测试模块导入"""
    try:
        import app
        import config
        import app_config
        print("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置"""
    try:
        from app_config import config
        
        # 测试配置类
        dev_config = config['development']
        prod_config = config['production']
        
        print("✓ 配置文件加载成功")
        print(f"  - 开发环境DEBUG: {dev_config.DEBUG}")
        print(f"  - 生产环境DEBUG: {prod_config.DEBUG}")
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from config import get_db_connection
        
        conn = get_db_connection()
        if conn:
            conn.close()
            print("✓ 数据库连接成功")
            return True
        else:
            print("✗ 数据库连接失败")
            return False
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False

def test_app_creation():
    """测试Flask应用创建"""
    try:
        from app import app
        
        if app:
            print("✓ Flask应用创建成功")
            print(f"  - 应用名称: {app.name}")
            print(f"  - 调试模式: {app.debug}")
            return True
        else:
            print("✗ Flask应用创建失败")
            return False
    except Exception as e:
        print(f"✗ Flask应用测试失败: {e}")
        return False

def test_routes():
    """测试路由注册"""
    try:
        from app import app
        
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(f"{rule.rule} -> {rule.endpoint}")
        
        print(f"✓ 路由注册成功，共 {len(routes)} 个路由")
        
        # 显示主要路由
        main_routes = [r for r in routes if any(endpoint in r for endpoint in 
                      ['index', 'login', 'add_patient', 'search_patient', 'appointments'])]
        
        print("  主要路由:")
        for route in main_routes[:5]:  # 只显示前5个
            print(f"    {route}")
        
        return True
    except Exception as e:
        print(f"✗ 路由测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    required_files = [
        'app.py',
        'config.py',
        'app_config.py',
        'requirements.txt',
        'create_tables.sql',
        'README.md'
    ]
    
    required_dirs = [
        'templates',
        'static',
        'uploads'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    for dir in required_dirs:
        if not os.path.exists(dir):
            missing_dirs.append(dir)
    
    if not missing_files and not missing_dirs:
        print("✓ 文件结构完整")
        return True
    else:
        if missing_files:
            print(f"✗ 缺少文件: {', '.join(missing_files)}")
        if missing_dirs:
            print(f"✗ 缺少目录: {', '.join(missing_dirs)}")
        return False

def test_template_files():
    """测试模板文件"""
    template_dir = 'templates'
    if not os.path.exists(template_dir):
        print("✗ templates目录不存在")
        return False
    
    required_templates = [
        'index.html',
        'login.html',
        'add_patient.html',
        'patient_records.html'
    ]
    
    missing_templates = []
    for template in required_templates:
        template_path = os.path.join(template_dir, template)
        if not os.path.exists(template_path):
            missing_templates.append(template)
    
    if not missing_templates:
        template_count = len([f for f in os.listdir(template_dir) if f.endswith('.html')])
        print(f"✓ 模板文件完整，共 {template_count} 个模板")
        return True
    else:
        print(f"✗ 缺少模板文件: {', '.join(missing_templates)}")
        return False

def main():
    """运行所有测试"""
    print("=" * 50)
    print("孕产妇管理系统 - 应用测试")
    print("=" * 50)
    
    tests = [
        ("文件结构检查", test_file_structure),
        ("模板文件检查", test_template_files),
        ("模块导入测试", test_imports),
        ("配置测试", test_config),
        ("Flask应用测试", test_app_creation),
        ("路由测试", test_routes),
        ("数据库连接测试", test_database_connection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用已准备就绪。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查上述错误。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
