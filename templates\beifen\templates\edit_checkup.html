<!DOCTYPE html>
<html>
<head>
    <title>编辑产检记录 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button secondary">
            <i class="fas fa-home"></i> 返回首页
        </a>
        <a href="{{ url_for('patient_records', patient_id=record.patient_id) }}" class="button secondary">
            <i class="fas fa-arrow-left"></i> 返回孕产妇记录
        </a>
        
    </div>

    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1><i class="fas fa-edit"></i> 编辑产检记录</h1>
            <p class="subtitle">修改产检记录信息</p>
        </div>

        <!-- 孕产妇信息摘要 -->
        <div class="patient-summary">
            <h2><i class="fas fa-user"></i> 孕产妇信息</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <label>姓名：</label>
                    <span>{{ record.patient_name }}</span>
                </div>
                <div class="summary-item">
                    <label>年龄：</label>
                    <span>{{ record.age }}岁</span>
                </div>
                <div class="summary-item">
                    <label>门诊号：</label>
                    <span>{{ record.outpatient_number }}</span>
                </div>
                <div class="summary-item">
                    <label>联系电话：</label>
                    <span>{{ record.phone }}</span>
                </div>
            </div>
        </div>

        <form method="post" class="checkup-form">
            <!-- 就诊信息 -->
            <div class="form-section">
                <h2><i class="fas fa-stethoscope"></i> 就诊信息</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="checkup_date">就诊日期</label>
                        <input type="date" id="checkup_date" name="checkup_date" 
                               value="{{ record.checkup_date }}" required>
                    </div>
                    <div class="form-group">
                        <label for="doctor">主治医生</label>
                        <input type="text" id="doctor" name="doctor" 
                               value="{{ record.doctor }}" required>
                    </div>
                    <div class="form-group">
                        <label for="appointment_date">下次预约日期</label>
                        <input type="date" id="appointment_date" name="appointment_date"
                               value="{{ record.appointment_date }}">
                    </div>
                </div>
            </div>

            <!-- 风险评估 -->
            <div class="form-section">
                <h2><i class="fas fa-exclamation-triangle"></i> 风险评估</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="risk_color">高危等级</label>
                        <select id="risk_color" name="risk_color">
                            <option value="" {% if not record.risk_color %}selected{% endif %}>无</option>
                            <option value="红" {% if record.risk_color == '红' %}selected{% endif %}>红</option>
                            <option value="黄" {% if record.risk_color == '黄' %}selected{% endif %}>黄</option>
                            <option value="蓝" {% if record.risk_color == '蓝' %}selected{% endif %}>蓝</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="risk_factors">高危因素</label>
                        <textarea id="risk_factors" name="risk_factors" rows="3">{{ record.risk_factors }}</textarea>
                    </div>
                </div>
            </div>

            <!-- 体征数据 -->
            <div class="form-section">
                <h2><i class="fas fa-heartbeat"></i> 体征数据</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="fundal_height">宫底高度 (cm)</label>
                        <input type="number" id="fundal_height" name="fundal_height" 
                               value="{{ record.fundal_height }}" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="abdominal_circumference">腹围 (cm)</label>
                        <input type="number" id="abdominal_circumference" name="abdominal_circumference" 
                               value="{{ record.abdominal_circumference }}" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="weight">体重 (kg)</label>
                        <input type="number" id="weight" name="weight" 
                               value="{{ record.weight }}" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="blood_pressure">血压 (mmHg)</label>
                        <input type="text" id="blood_pressure" name="blood_pressure" 
                               value="{{ record.blood_pressure }}" placeholder="如：120/80">
                    </div>
                    <div class="form-group">
                        <label for="fetal_heart_rate">胎心率 (次/分)</label>
                        <input type="text" id="fetal_heart_rate" name="fetal_heart_rate" 
                               value="{{ record.fetal_heart_rate }}">
                    </div>
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="form-section">
                <h2><i class="fas fa-clipboard"></i> 备注信息</h2>
                <div class="form-group">
                    <textarea id="notes" name="notes" rows="4" 
                              placeholder="请输入备注信息...">{{ record.notes }}</textarea>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="button">
                    <i class="fas fa-save"></i> 保存修改
                </button>
            </div>
        </form>

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert {{ category }}">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <style>
        .form-actions {
            margin-top: calc(var(--spacing-unit) * 4);
            text-align: center;
        }
        
        .form-actions .button {
            padding: calc(var(--spacing-unit) * 2) calc(var(--spacing-unit) * 4);
            font-size: 1.1rem;
        }
        
        .form-section {
            margin-bottom: calc(var(--spacing-unit) * 4);
        }
        
        .form-section:last-child {
            margin-bottom: 0;
        }
        
        .patient-summary {
            margin-bottom: calc(var(--spacing-unit) * 4);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: calc(var(--spacing-unit) * 2);
            margin-top: calc(var(--spacing-unit) * 2);
        }
        
        .summary-item {
            display: flex;
            gap: var(--spacing-unit);
        }
        
        .summary-item label {
            color: var(--text-secondary);
        }
        
        .summary-item span {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .form-actions .button {
                width: 100%;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html> 