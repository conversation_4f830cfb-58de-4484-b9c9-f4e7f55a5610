<!DOCTYPE html>
<html>
<head>
    <title>导入孕产妇信息 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .import-instructions {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
        }

        .template-download {
            margin-bottom: 2rem;
            text-align: center;
        }

        .template-download .button {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        .import-form {
            max-width: 500px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <h1>导入孕产妇信息</h1>
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button">返回首页</a>
    </div>

    <div class="import-container">
        <div class="import-instructions">
            <h2>导入说明</h2>
            <p>1. 点击"下载导入模板"按钮获取最新的Excel模板</p>
            <p>2. 按照模板格式填写孕产妇信息（日期格式：YYYY-MM-DD）</p>
            <p>3. 选择填写好的Excel文件并点击"开始导入"</p>
            <p>注意：请不要修改模板的列名和格式，以确保数据正确导入</p>
        </div>

        <div class="template-download">
            <a href="{{ url_for('export_template') }}" class="button">
                <i class="fas fa-download"></i> 下载导入模板
            </a>
        </div>

        <form method="POST" enctype="multipart/form-data" class="import-form">
            <div class="form-group">
                <label for="file">选择Excel文件</label>
                <input type="file" id="file" name="file" accept=".xlsx,.xls" required>
            </div>
            <div class="form-group">
                <button type="submit" class="submit-btn">开始导入</button>
            </div>
        </form>
    </div>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
            {% for message in messages %}
                <div class="alert">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}
</body>
</html> 