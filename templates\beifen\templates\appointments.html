<!DOCTYPE html>
<html>
<head>
    <title>预约查询 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .appointments-container {
            padding: 20px;
        }
        
        .appointment-section {
            margin-bottom: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .appointment-section h2 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .count {
            font-size: 0.9em;
            color: #666;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .vip {
            background-color: #fff3cd;
        }
        
        .high-risk {
            background-color: #f8d7da;
        }
        
        .button {
            padding: 6px 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            margin: 2px;
        }
        
        .button.small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .mark-notified {
            background-color: #28a745;
            color: white;
        }
        
        .unmark-notified {
            background-color: #6c757d;
            color: white;
        }
        
        .risk-color {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            color: white;
        }
        
        .yellow { background-color: #ffc107; }
        .orange { background-color: #fd7e14; }
        .red { background-color: #dc3545; }
        
        .no-data {
            text-align: center;
            color: #6c757d;
            padding: 20px;
        }
    </style>
</head>
<body>
    <h1>预约查询</h1>
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button">返回首页</a>
    </div>

    <div class="appointments-container">
        <!-- 已过期预约 -->
        <div class="appointment-section">
            <h2>已过期预约 <span class="count">({{ overdue_appointments|length }})</span></h2>
            {% if overdue_appointments %}
                <table>
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>门诊病历号</th>
                            <th>联系电话</th>
                            <th>上次产检</th>
                            <th>预约日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appt in overdue_appointments %}
                            <tr class="{% if appt.is_vip %}vip{% endif %} {% if appt.risk_color %}high-risk{% endif %}">
                                <td>{{ appt.name }}</td>
                                <td>{{ appt.outpatient_number }}</td>
                                <td>{{ appt.phone }}</td>
                                <td>{{ appt.checkup_date }}</td>
                                <td>{{ appt.appointment_date }}</td>
                                <td>
                                    {% if appt.risk_color %}
                                        <span class="risk-color {{ appt.risk_color }}">{{ appt.risk_color }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('patient_records', patient_id=appt.patient_id) }}" class="button small">查看记录</a>
                                    <a href="{{ url_for('add_checkup', patient_id=appt.patient_id) }}" class="button small">添加产检</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="no-data">暂无过期预约</p>
            {% endif %}
        </div>

        <!-- 即将到期预约 -->
        <div class="appointment-section">
            <h2>即将到期预约 (7天内) <span class="count">({{ upcoming_appointments|length }})</span></h2>
            {% if upcoming_appointments %}
                <table>
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>门诊病历号</th>
                            <th>联系电话</th>
                            <th>预约日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appt in upcoming_appointments %}
                        {% if not appt.notification_status %}
                            <tr class="{% if appt.is_vip %}vip{% endif %} {% if appt.risk_color %}high-risk{% endif %}">
                                <td>{{ appt.name }}</td>
                                <td>{{ appt.outpatient_number }}</td>
                                <td>{{ appt.phone }}</td>
                                <td>{{ appt.appointment_date }}</td>
                                <td>
                                    {% if appt.risk_color %}
                                        <span class="risk-color {{ appt.risk_color }}">{{ appt.risk_color }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="button small mark-notified" data-record-id="{{ appt.record_id }}">
                                        标记已通知
                                    </button>
                                    <a href="{{ url_for('patient_records', patient_id=appt.patient_id) }}" class="button small">查看记录</a>
                                </td>
                            </tr>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="no-data">暂无即将到期预约</p>
            {% endif %}
        </div>

        <!-- 已通知预约 -->
        <div class="appointment-section">
            <h2>已通知预约 <span class="count">({{ upcoming_appointments|selectattr('notification_status')|list|length }})</span></h2>
            {% if upcoming_appointments|selectattr('notification_status')|list %}
                <table>
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>门诊病历号</th>
                            <th>联系电话</th>
                            <th>预约日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appt in upcoming_appointments %}
                        {% if appt.notification_status %}
                            <tr class="{% if appt.is_vip %}vip{% endif %} {% if appt.risk_color %}high-risk{% endif %}">
                                <td>{{ appt.name }}</td>
                                <td>{{ appt.outpatient_number }}</td>
                                <td>{{ appt.phone }}</td>
                                <td>{{ appt.appointment_date }}</td>
                                <td>
                                    {% if appt.risk_color %}
                                        <span class="risk-color {{ appt.risk_color }}">{{ appt.risk_color }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="button small unmark-notified" data-record-id="{{ appt.record_id }}">
                                        取消标记
                                    </button>
                                    <a href="{{ url_for('patient_records', patient_id=appt.patient_id) }}" class="button small">查看记录</a>
                                </td>
                            </tr>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="no-data">暂无已通知预约</p>
            {% endif %}
        </div>
    </div>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
            {% for message in messages %}
                <div class="alert">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- JavaScript 代码 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 标记已通知
        document.querySelectorAll('.mark-notified').forEach(button => {
            button.addEventListener('click', function() {
                const recordId = this.dataset.recordId;
                updateNotificationStatus(recordId, true);
            });
        });

        // 取消标记
        document.querySelectorAll('.unmark-notified').forEach(button => {
            button.addEventListener('click', function() {
                const recordId = this.dataset.recordId;
                updateNotificationStatus(recordId, false);
            });
        });

        function updateNotificationStatus(recordId, status) {
            fetch('/update_notification_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    record_id: recordId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('更新失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请重试');
            });
        }
    });
    </script>
</body>
</html> 