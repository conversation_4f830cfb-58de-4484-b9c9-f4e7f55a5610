from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, session
import hashlib
from config import get_db_connection
from app_config import config
from datetime import datetime, timedelta
import pandas as pd
from werkzeug.utils import secure_filename
import os
import pymysql
from io import BytesIO
from openpyxl.utils import get_column_letter
import re
from functools import wraps

# 创建Flask应用
app = Flask(__name__)

# 加载配置
config_name = os.environ.get('FLASK_CONFIG') or 'default'
app.config.from_object(config[config_name])

# 确保上传文件夹存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 检查用户是否已登录的装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录')
            return redirect(url_for('login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def calculate_due_date(last_menstrual_date):
    """根据末次月经计算预产期"""
    if last_menstrual_date:
        last_menstrual = datetime.strptime(last_menstrual_date, '%Y-%m-%d')
        due_date = (last_menstrual + timedelta(days=280)).strftime('%Y-%m-%d')
        return due_date
    return None

def format_outpatient_number(number):
    """格式化门诊号"""
    if not number:
        return None
    # 移除所有空白字符
    number = ''.join(str(number).split())
    return number.upper()

def validate_outpatient_number(number):
    """验证门诊号格式"""
    if not number:
        return False
    number = str(number).strip().upper()
    # 只需要验证是否包含数字
    return any(c.isdigit() for c in number)

def get_doctor_history():
    conn = get_db_connection()
    if conn is None:
        return []
    
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute("SELECT doctor_name FROM doctor_history ORDER BY created_at DESC")
        return [doctor['doctor_name'] for doctor in cursor.fetchall()]
    except Exception as e:
        print(f'获取医生列表错误：{str(e)}')
        return []
    finally:
        cursor.close()
        conn.close()

@app.route('/add_patient', methods=['GET', 'POST'])
@login_required
def add_patient():
    doctors = get_doctor_history()  # Fetch the list of doctors
    if request.method == 'POST':
        conn = get_db_connection()
        if conn is None:
            flash('无法连接到数据库，请检查数据库配置')
            return redirect(url_for('index'))
            
        cursor = conn.cursor()
        try:
            # 验证必填字段
            required_fields = {
                'name': '姓名',
                'age': '年龄',
                'id_card': '身份证号',
                'phone': '联系电话',
                'address': '地址',
                'last_menstrual_date': '末次月经',
                'due_date': '预产期',
                'first_visit_date': '初诊日期',
                'registration_date': '建册日期',
                'doctor_in_charge': '主管医生'
            }
            
            for field, field_name in required_fields.items():
                if not request.form.get(field):
                    flash(f'{field_name}不能为空')
                    return redirect(url_for('add_patient'))

            # 格式化门诊号
            outpatient_number = format_outpatient_number(request.form.get('outpatient_number'))
            if outpatient_number and not validate_outpatient_number(outpatient_number):
                flash('门诊号格式不正确，应以MZ开头')
                return redirect(url_for('add_patient'))

            # 检查电话号码是否已存在
            phone = request.form.get('phone')
            cursor.execute("SELECT * FROM patient_info WHERE phone = %s", (phone,))
            existing_patient = cursor.fetchone()

            if existing_patient:
                # 如果电话号码已存在，更新该记录
                sql = """UPDATE patient_info SET 
                    name = %s,
                    age = %s,
                    id_card = %s,
                    outpatient_number = %s,
                    address = %s,
                    husband_name = %s,
                    due_date = %s,
                    last_menstrual_date = %s,
                    first_visit_date = %s,
                    registration_date = %s,
                    doctor_in_charge = %s,
                    management_org = %s,
                    is_high_risk = %s,
                    is_vip = %s,
                    pregnancy_count = %s,
                    delivery_count = %s,
                    risk_factors = %s,
                    has_given_birth = %s
                WHERE phone = %s"""
                
                # 如果是在孕状态，根据末次月经计算预产期
                last_menstrual_date = request.form.get('last_menstrual_date')
                has_given_birth = request.form.get('has_given_birth', '在孕')
                
                if has_given_birth == '在孕' and last_menstrual_date:
                    due_date = calculate_due_date(last_menstrual_date)
                else:
                    due_date = request.form.get('due_date')

                values = (
                    request.form.get('name'),
                    int(request.form.get('age')),
                    request.form.get('id_card'),
                    outpatient_number,
                    request.form.get('address'),
                    request.form.get('husband_name'),
                    due_date,  # 使用计算得到的预产期
                    request.form.get('last_menstrual_date'),
                    request.form.get('first_visit_date'),
                    request.form.get('registration_date'),
                    request.form.get('doctor_in_charge'),
                    request.form.get('management_org', '涟源市妇幼保健院'),
                    '高危' if request.form.get('is_high_risk') == 'true' else '否',
                    request.form.get('is_vip') == 'true',
                    int(request.form.get('pregnancy_count', 1)),
                    int(request.form.get('delivery_count', 0)),
                    request.form.get('risk_factors', ''),
                    has_given_birth,
                    phone  # 用于更新的条件
                )
                
                cursor.execute(sql, values)
                conn.commit()  # 提交事务
                flash('孕产妇信息更新成功！', 'success')
            else:
                # 如果电话号码不存在，插入新记录
                sql = """INSERT INTO patient_info (
                    name, age, id_card, outpatient_number, address, phone,
                    husband_name, due_date, last_menstrual_date, first_visit_date,
                    registration_date, doctor_in_charge, management_org, is_high_risk,
                    is_vip, pregnancy_count, delivery_count, risk_factors, has_given_birth
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                
                # 如果是在孕状态，根据末次月经计算预产期
                last_menstrual_date = request.form.get('last_menstrual_date')
                has_given_birth = request.form.get('has_given_birth', '在孕')
                
                if has_given_birth == '在孕' and last_menstrual_date:
                    due_date = calculate_due_date(last_menstrual_date)
                else:
                    due_date = request.form.get('due_date')

                values = (
                    request.form.get('name'),
                    int(request.form.get('age')),
                    request.form.get('id_card'),
                    outpatient_number,
                    request.form.get('address'),
                    phone,
                    request.form.get('husband_name'),
                    due_date,  # 使用计算得到的预产期
                    request.form.get('last_menstrual_date'),
                    request.form.get('first_visit_date'),
                    request.form.get('registration_date'),
                    request.form.get('doctor_in_charge'),
                    request.form.get('management_org', '涟源市妇幼保健院'),
                    '高危' if request.form.get('is_high_risk') == 'true' else '否',
                    request.form.get('is_vip') == 'true',
                    int(request.form.get('pregnancy_count', 1)),
                    int(request.form.get('delivery_count', 0)),
                    request.form.get('risk_factors', ''),
                    has_given_birth
                )
                
                cursor.execute(sql, values)
                conn.commit()  # 提交事务
                flash('孕产妇信息添加成功！', 'success')

            # 获取新添加的孕产妇ID
            cursor.execute("SELECT LAST_INSERT_ID()")
            patient_id = cursor.fetchone()[0]
            
            return redirect(url_for('patient_records', patient_id=patient_id))
            
        except Exception as e:
            conn.rollback()  # 回滚事务
            flash(f'错误：{str(e)}')
            return redirect(url_for('index'))
            
        finally:
            cursor.close()
            conn.close()
        
    return render_template('add_patient.html', doctors=doctors)

@app.route('/add_checkup/<int:patient_id>', methods=['GET', 'POST'])
@login_required
def add_checkup(patient_id):
    conn = get_db_connection()
    if conn is None:
        error_message = '无法连接到数据库，请检查数据库配置'
        return render_template('add_checkup.html', patient_id=patient_id, error=error_message)
    
    cursor = conn.cursor(dictionary=True)  # 确保获取字典格式
    try:
        if request.method == 'GET':
            # 获取病人信息
            cursor.execute('SELECT * FROM patient_info WHERE id = %s', (patient_id,))
            patient = cursor.fetchone()
            
            if not patient:
                error_message = '未找到孕产妇信息'
                return render_template('add_checkup.html', patient_id=patient_id, error=error_message)
            
            # 获取最近一次的产检记录
            cursor.execute('''
                SELECT * FROM checkup_records 
                WHERE patient_id = %s 
                ORDER BY checkup_date DESC 
                LIMIT 1
            ''', (patient_id,))
            last_record = cursor.fetchone()
            
            # 准备默认数据
            default_data = {}
            if last_record:
                default_data = {
                    'doctor': last_record['doctor'],
                    'risk_factors': last_record['risk_factors'],
                    'risk_color': last_record['risk_color'],
                    'fundal_height': last_record['fundal_height'],
                    'abdominal_circumference': last_record['abdominal_circumference'],
                    'blood_pressure': last_record['blood_pressure'],
                    'weight': last_record['weight'],
                    'fetal_heart_rate': last_record['fetal_heart_rate']
                }
            else:
                default_data = {
                    'doctor': '',
                    'risk_factors': patient['risk_factors'] if patient['risk_factors'] else '',
                    'risk_color': '',
                    'fundal_height': 0,
                    'abdominal_circumference': 0,
                    'blood_pressure': 0,
                    'weight': 0,
                    'fetal_heart_rate': 0,
                }
            
            # 设置默认的产检日期为今天
            default_data['checkup_date'] = datetime.now().strftime('%Y-%m-%d')
            
            return render_template('add_checkup.html', 
                                 patient=patient, 
                                 patient_id=patient_id, 
                                 last_record=last_record,
                                 default_data=default_data)
        
        elif request.method == 'POST':
            # 处理表单提交
            doctor = request.form.get('doctor')
            risk_factors = request.form.get('risk_factors')
            risk_color = request.form.get('risk_color')
            checkup_date = request.form.get('checkup_date')
            appointment_date = request.form.get('appointment_date')
            fundal_height = request.form.get('fundal_height')
            abdominal_circumference = request.form.get('abdominal_circumference')
            blood_pressure = request.form.get('blood_pressure')
            weight = request.form.get('weight')
            fetal_heart_rate = request.form.get('fetal_heart_rate')
            notes = request.form.get('notes')

            # 检查下次预约时间是否填写
            if not appointment_date:
                flash('下次预约时间不能为空！')
                return redirect(url_for('add_checkup', patient_id=patient_id))

            # 插入产检记录
            sql = """INSERT INTO checkup_records (
                patient_id, doctor, risk_factors, risk_color,
                checkup_date, appointment_date, fundal_height, abdominal_circumference,
                blood_pressure, weight, fetal_heart_rate, notes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
            
            values = (
                patient_id,
                doctor,
                risk_factors,
                risk_color,
                checkup_date,
                appointment_date,
                fundal_height,
                abdominal_circumference,
                blood_pressure,
                weight,
                fetal_heart_rate,
                notes
            )
            
            cursor.execute(sql, values)
            conn.commit()  # 提交事务
            flash('产检记录添加成功！', 'success')
            return redirect(url_for('patient_records', patient_id=patient_id))

    except Exception as e:
        error_message = f'错误：{str(e)}'
        return render_template('add_checkup.html', patient_id=patient_id, error=error_message)
    finally:
        cursor.close()
        conn.close()

@app.route('/patient_records/<int:patient_id>')
@login_required
def patient_records(patient_id):
    conn = get_db_connection()
    if conn is None:
        flash('无法连接到数据库，请检查数据库配置')
        return redirect(url_for('index'))
        
    cursor = conn.cursor(dictionary=True)
    try:
        # 获取孕产妇基本信息
        cursor.execute("""
            SELECT *, 
                FLOOR(DATEDIFF(CURDATE(), last_menstrual_date) / 7) as pregnancy_weeks,
                DATEDIFF(CURDATE(), last_menstrual_date) % 7 as pregnancy_days
            FROM patient_info 
            WHERE id = %s
        """, (patient_id,))
        
        patient = cursor.fetchone()
        if patient:
            # 计算当前孕周
            if patient['has_given_birth'] in ['已分娩', '已流产', '已引产']:
                patient['pregnancy_week'] = patient['has_given_birth']
            elif patient['last_menstrual_date']:
                weeks = patient['pregnancy_weeks']
                days = patient['pregnancy_days']
                patient['pregnancy_week'] = f"{weeks}周{days}天"
            else:
                patient['pregnancy_week'] = "未知"
                
            # 获取产检记录
            cursor.execute("""
                SELECT * FROM checkup_records 
                WHERE patient_id = %s 
                ORDER BY checkup_date DESC
            """, (patient_id,))
            records = cursor.fetchall()
            
            return render_template('patient_records.html', patient=patient, records=records)
        else:
            flash('未找到孕产妇信息')
            return redirect(url_for('index'))
            
    except Exception as e:
        flash(f'错误：{str(e)}')
        return redirect(url_for('index'))
        
    finally:
        cursor.close()
        conn.close()

@app.route('/search_patient', methods=['GET', 'POST'])
@login_required
def search_patient():
    patients = []
    if request.method == 'POST':
        search_term = request.form.get('search_term')
        conn = get_db_connection()
        if conn is None:
            flash('无法连接到数据库，请检查数据库配置')
            return render_template('search_patient.html', patients=patients)
            
        cursor = conn.cursor(dictionary=True)
        try:
            # 搜索姓名、身份证号或电话
            sql = """SELECT * FROM patient_info 
                    WHERE name LIKE %s 
                    OR id_card LIKE %s 
                    OR phone LIKE %s"""
            search_pattern = f'%{search_term}%'
            cursor.execute(sql, (search_pattern, search_pattern, search_pattern))
            patients = cursor.fetchall()
            
        except Exception as e:
            flash(f'搜索出错：{str(e)}')
        finally:
            cursor.close()
            conn.close()
            
    return render_template('search_patient.html', patients=patients)

@app.route('/static/<path:filename>')
def serve_static(filename):
    try:
        return app.send_static_file(filename)
    except Exception as e:
        flash(f'文件不存在：{filename}')
        return redirect(url_for('import_patients'))

def validate_id_card(id_card):
    # 简化的身份证校验（实际需完整校验）
    return True

def validate_phone_number(phone):
    """验证电话号码格式"""
    # 移除所有空格和破折号
    phone = re.sub(r'[\s-]', '', str(phone))
    
    # 手机号码格式：1开头的11位数字
    mobile_pattern = r'^1[3-9]\d{9}$'
    
    # 座机号码格式：
    # - 区号：2-4位数字，以0开头
    # - 号码：7-8位数字
    landline_pattern = r'^0\d{2,3}-?\d{7,8}$'
    
    return bool(re.match(mobile_pattern, phone) or re.match(landline_pattern, phone))

@app.route('/import_patients', methods=['GET', 'POST'])
@login_required
def import_patients():
    template_path = os.path.join('static', 'template.xlsx')
    template_exists = os.path.exists(template_path)

    if request.method == 'POST':
        # ------ 1. 文件验证增强 ------
        if 'file' not in request.files:
            flash('请选择文件')
            return redirect(request.url)
            
        file = request.files['file']
        if not file or file.filename == '':
            flash('未选择文件')
            return redirect(request.url)
            
        if not allowed_file(file.filename):
            flash('仅支持.xlsx文件')
            return redirect(request.url)

        # 生成唯一文件名防止并发冲突
        filename = secure_filename(f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file.filename}")
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        try:
            # ------ 2. 使用更安全的Excel读取方式 ------
            df = pd.read_excel(filepath, engine='openpyxl')  # 读取时使用 openpyxl
            if df.empty:
                flash('文件内容为空')
                return redirect(request.url)

            # 对Excel数据进行去重处理
            if '联系电话' in df.columns:
                # 保留最后出现的记录（后面的覆盖前面的）
                df = df.drop_duplicates(subset=['联系电话'], keep='last')
                
                # 如果同时有身份证号，也按身份证号去重
                if '身份证号' in df.columns:
                    df = df.drop_duplicates(subset=['身份证号'], keep='last')

            # ------ 3. 批量处理数据 ------
            processed_data = []
            existing_phones = set()
            errors = []

            # 预加载数据库中已存在的电话号码（批量查询优化）
            conn = get_db_connection()
            if conn is None:
                flash('数据库连接失败')
                return redirect(request.url)
            
            with conn.cursor() as cursor:
                cursor.execute("SELECT phone FROM patient_info")
                existing_phones = {row[0] for row in cursor.fetchall()}

            # ------ 4. 数据预处理与验证 ------
            for index, row in df.iterrows():
                row_number = index + 2  # Excel行号从2开始
                try:
                    # 检查空行
                    if row.isnull().all():
                        continue

                    # 必填字段校验
                    required_fields = {'姓名', '身份证号'}
                    missing_fields = [field for field in required_fields if pd.isna(row.get(field))]
                    if missing_fields:
                        errors.append(f"第{row_number}行缺失必填字段: {', '.join(missing_fields)}")
                        continue

                    # 电话号码处理和验证
                    phone = str(row['联系电话']).strip()
                    phone = re.sub(r'[\s-]', '', phone)  # 移除空格和破折号
                    if not validate_phone_number(phone):
                        errors.append(f"第{row_number}行联系电话格式错误，支持手机号或座机号(区号-号码)")
                        continue

                    id_card = str(row['身份证号']).strip()
                    if not validate_id_card(id_card):
                        errors.append(f"第{row_number}行身份证号无效")
                        continue

                    # 门诊号处理
                    outpatient_number = format_outpatient_number(row.get('门诊号')) if pd.notna(row.get('门诊号')) else None
                    if outpatient_number and not validate_outpatient_number(outpatient_number):
                        errors.append(f"第{row_number}行门诊号格式错误")
                        continue

                    # 日期字段解析
                    date_fields = ['预产期', '末次月经', '首次产检日期', '登记日期']
                    date_data = {}
                    for field in date_fields:
                        if pd.notna(row.get(field)):
                            try:
                                date_data[field] = pd.to_datetime(row[field]).date()
                            except:
                                errors.append(f"第{row_number}行{field}日期格式无效")
                                continue

                    # 构造数据对象
                    data = {
                        'name': str(row['姓名']).strip() if pd.notna(row.get('姓名')) else '',
                        'outpatient_number': format_outpatient_number(row.get('门诊号')) if pd.notna(row.get('门诊号')) else None,
                        'phone': phone,  # 使用处理后的电话号码
                        'age': int(row['年龄']) if pd.notna(row.get('年龄')) else None,
                        'id_card': str(row['身份证号']).strip() if pd.notna(row.get('身份证号')) else '',
                        'address': str(row.get('地址', '')).strip() if pd.notna(row.get('地址')) else '',
                        'husband_name': str(row.get('丈夫姓名', '')).strip() if pd.notna(row.get('丈夫姓名')) else '',
                        'due_date': date_data.get('预产期'),
                        'last_menstrual_date': date_data.get('末次月经'),
                        'first_visit_date': date_data.get('首次产检日期'),
                        'registration_date': date_data.get('登记日期'),
                        'doctor_in_charge': str(row.get('主管医生', '')).strip() if pd.notna(row.get('主管医生')) else '',
                        'management_org': str(row.get('管理机构', '涟源市妇幼保健院')).strip() if pd.notna(row.get('管理机构')) else '涟源市妇幼保健院',
                        'pregnancy_count': int(row['怀孕次数']) if pd.notna(row.get('怀孕次数')) else 1,
                        'delivery_count': int(row['分娩次数']) if pd.notna(row.get('分娩次数')) else 0,
                        'is_high_risk': '高危' if str(row.get('是否高危', '')).strip() in ['是', 'true', '1', '高危'] else '否',
                        'risk_factors': str(row.get('高危因素', '')).strip() if pd.notna(row.get('高危因素')) else '',
                        'is_vip': str(row.get('是否VIP', '')).strip() == '是',
                        'has_given_birth': str(row.get('分娩状态', '在孕')).strip() 
                        if pd.notna(row.get('分娩状态')) else '在孕'
                    }

                    processed_data.append(data)

                except Exception as e:
                    errors.append(f"第{row_number}行数据处理失败: {str(e)}")
                    continue

            # ------ 5. 批量数据库操作（事务管理） ------
            if errors:
                flash(f"发现 {len(errors)} 处错误: {'; '.join(errors[:5])}{'...' if len(errors)>5 else ''}")
                return redirect(request.url)

            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    # 分批次处理（例如每100条提交一次）
                    batch_size = 100
                    total_updates = 0

                    for i in range(0, len(processed_data), batch_size):
                        batch = processed_data[i:i+batch_size]
                        
                        for data in batch:
                            # 使用 INSERT ... ON DUPLICATE KEY UPDATE
                            sql = """INSERT INTO patient_info (
                                name, age, id_card, outpatient_number, address, phone,
                                husband_name, due_date, last_menstrual_date, first_visit_date,
                                registration_date, doctor_in_charge, management_org, is_high_risk,
                                is_vip, pregnancy_count, delivery_count, risk_factors, has_given_birth
                            ) VALUES (
                                %(name)s, %(age)s, %(id_card)s, %(outpatient_number)s, %(address)s, %(phone)s,
                                %(husband_name)s, %(due_date)s, %(last_menstrual_date)s, %(first_visit_date)s,
                                %(registration_date)s, %(doctor_in_charge)s, %(management_org)s, %(is_high_risk)s,
                                %(is_vip)s, %(pregnancy_count)s, %(delivery_count)s, %(risk_factors)s, %(has_given_birth)s
                            ) ON DUPLICATE KEY UPDATE 
                                name = VALUES(name),
                                age = VALUES(age),
                                id_card = VALUES(id_card),
                                outpatient_number = VALUES(outpatient_number),
                                address = VALUES(address),
                                husband_name = VALUES(husband_name),
                                due_date = VALUES(due_date),
                                last_menstrual_date = VALUES(last_menstrual_date),
                                first_visit_date = VALUES(first_visit_date),
                                registration_date = VALUES(registration_date),
                                doctor_in_charge = VALUES(doctor_in_charge),
                                management_org = VALUES(management_org),
                                is_high_risk = VALUES(is_high_risk),
                                is_vip = VALUES(is_vip),
                                pregnancy_count = VALUES(pregnancy_count),
                                delivery_count = VALUES(delivery_count),
                                risk_factors = VALUES(risk_factors),
                                has_given_birth = VALUES(has_given_birth)"""
                            
                            cursor.execute(sql, data)
                            total_updates += cursor.rowcount

                        conn.commit()

                    # 处理完成后显示结果
                    flash(f"成功导入/更新 {total_updates} 条记录")

            except Exception as e:
                conn.rollback()
                flash(f"数据库操作失败: {str(e)}")
                return redirect(request.url)

        except Exception as e:
            flash(f"文件处理失败: {str(e)}")
        finally:
            if os.path.exists(filepath):
                os.remove(filepath)

        return redirect(url_for('index'))

    return render_template('import_patients.html', template_exists=template_exists)

@app.route('/checkup_detail/<int:record_id>')
def checkup_detail(record_id):
    conn = get_db_connection()
    if conn is None:
        flash('无法连接到数据库，请检查数据库配置')
        return redirect(url_for('index'))
        
    cursor = conn.cursor(dictionary=True)
    try:
        # 修改 SQL 查询，包含门诊病历号
        cursor.execute("""
            SELECT cr.*, pi.name as patient_name, pi.age, pi.phone, pi.outpatient_number
            FROM checkup_records cr 
            JOIN patient_info pi ON cr.patient_id = pi.id 
            WHERE cr.record_id = %s
        """, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            flash('记录不存在')
            return redirect(url_for('index'))
            
        return render_template('checkup_detail.html', record=record)
        
    except Exception as e:
        flash(f'错误：{str(e)}')
        return redirect(url_for('index'))
    finally:
        cursor.close()
        conn.close()

@app.route('/update_patient/<int:patient_id>', methods=['POST'])
def update_patient(patient_id):
    # 获取表单数据
    name = request.form.get('name')
    outpatient_number = request.form.get('outpatient_number')
    id_card = request.form.get('id_card')
    age = request.form.get('age')
    pregnancy_count = request.form.get('pregnancy_count')
    delivery_count = request.form.get('delivery_count')
    phone = request.form.get('phone')
    address = request.form.get('address')
    husband_name = request.form.get('husband_name')
    last_menstrual_date = request.form.get('last_menstrual_date') or None
    due_date = request.form.get('due_date') or None
    is_high_risk = request.form.get('is_high_risk') == 'true'
    risk_factors = request.form.get('risk_factors')
    doctor_in_charge = request.form.get('doctor_in_charge')
    management_org = request.form.get('management_org')
    is_vip = request.form.get('is_vip') == 'true'
    has_given_birth = request.form.get('has_given_birth', '在孕')

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # 如果是在孕状态，根据末次月经计算预产期
        if has_given_birth == '在孕' and last_menstrual_date:
            due_date = calculate_due_date(last_menstrual_date)

        # 更新数据
        cursor.execute("""
            UPDATE patient_info
            SET name = %s,
                outpatient_number = %s,
                id_card = %s,
                age = %s,
                pregnancy_count = %s,
                delivery_count = %s,
                phone = %s,
                address = %s,
                husband_name = %s,
                last_menstrual_date = %s,
                due_date = %s,
                is_high_risk = %s,
                risk_factors = %s,
                doctor_in_charge = %s,
                management_org = %s,
                is_vip = %s,
                has_given_birth = %s
            WHERE id = %s
        """, (name, outpatient_number, id_card, age, pregnancy_count, delivery_count, 
              phone, address, husband_name, last_menstrual_date, due_date,
              '高危' if is_high_risk else '否', risk_factors, doctor_in_charge, 
              management_org, is_vip, has_given_birth, patient_id))

        # 提交事务
        conn.commit()
        flash('信息更新成功！')
    except Exception as e:
        # 回滚事务
        conn.rollback()
        flash(f'更新失败：{str(e)}')
    finally:
        cursor.close()
        conn.close()

    return redirect(url_for('patient_records', patient_id=patient_id))

def init_db():
    conn = get_db_connection()
    if conn is None:
        print('无法连接到数据库，请检查数据库配置')
        return
        
    cursor = conn.cursor()
    try:
        # 创建主管医生历史记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS doctor_history (
                id INT AUTO_INCREMENT PRIMARY KEY,
                doctor_name VARCHAR(50) UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        conn.commit()
    except Exception as e:
        print(f'初始化数据库错误：{str(e)}')
    finally:
        cursor.close()
        conn.close()

@app.route('/appointments', methods=['GET'])
@login_required
def appointments():
    conn = get_db_connection()
    if conn is None:
        flash('无法连接到数据库，请检查数据库配置')
        return redirect(url_for('index'))
        
    cursor = conn.cursor(dictionary=True)
    try:
        # 获取所有医生列表
        cursor.execute("SELECT doctor_name FROM doctor_history ORDER BY doctor_name")
        doctors = cursor.fetchall()
        
        # 获取选中的医生
        selected_doctor = request.args.get('doctor')
        
        # 获取当前登录用户
        current_user = session.get('username')
        
        # 构建基础查询条件
        base_conditions = """
            WHERE cr.appointment_date <= CURDATE()
            AND cr.appointment_date >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
            AND pi.has_given_birth = '在孕'
            AND cr.checkup_date = (
                SELECT MAX(checkup_date) 
                FROM checkup_records 
                WHERE patient_id = cr.patient_id
            )
            AND cr.record_id = (
                SELECT MAX(record_id)
                FROM checkup_records
                WHERE patient_id = cr.patient_id
            )
        """
        
        # 如果选择了医生，添加医生筛选条件
        if selected_doctor:
            base_conditions += " AND cr.doctor = %s"
            params = [selected_doctor]
        # 如果当前用户是医生，只显示该医生的预约
        elif current_user in [d['doctor_name'] for d in doctors]:
            base_conditions += " AND cr.doctor = %s"
            params = [current_user]
        else:
            params = []
            
        # 获取已过期预约
        cursor.execute(f"""
            SELECT 
                cr.record_id,
                pi.name,
                pi.phone,
                pi.outpatient_number,
                cr.appointment_date,
                cr.checkup_date,
                cr.risk_color,
                pi.is_vip,
                pi.id as patient_id,
                cr.doctor,
                cr.notification_status
            FROM checkup_records cr
            JOIN patient_info pi ON cr.patient_id = pi.id
            {base_conditions}
            ORDER BY cr.appointment_date DESC
        """, params)
        overdue_appointments = cursor.fetchall()
        
        # 获取即将到期预约
        cursor.execute(f"""
            SELECT 
                cr.record_id,
                pi.name,
                pi.phone,
                cr.appointment_date,
                cr.notification_status,
                pi.id as patient_id,
                pi.outpatient_number,
                cr.risk_color,
                pi.is_vip,
                cr.doctor
            FROM checkup_records cr
            JOIN patient_info pi ON cr.patient_id = pi.id
            WHERE cr.appointment_date = DATE_ADD(CURDATE(), INTERVAL 7 DAY)
            AND pi.has_given_birth = '在孕'
            AND cr.record_id = (
                SELECT MAX(record_id)
                FROM checkup_records
                WHERE patient_id = cr.patient_id
            )
            {'AND cr.doctor = %s' if selected_doctor or (current_user in [d['doctor_name'] for d in doctors]) else ''}
            ORDER BY cr.appointment_date ASC
        """, [selected_doctor or current_user] if selected_doctor or (current_user in [d['doctor_name'] for d in doctors]) else [])
        
        upcoming_appointments = cursor.fetchall()
        
        return render_template('appointments.html', 
                             overdue_appointments=overdue_appointments,
                             upcoming_appointments=upcoming_appointments,
                             doctors=doctors,
                             selected_doctor=selected_doctor,
                             current_user=current_user)
        
    except Exception as e:
        flash(f'错误：{str(e)}')
        return redirect(url_for('index'))
        
    finally:
        cursor.close()
        conn.close()

@app.route('/export_records', methods=['GET', 'POST'])
@login_required
def export_records():
    if request.method == 'GET':
        return render_template('export_records.html')
        
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取筛选条件
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        min_weeks = request.form.get('min_weeks')
        max_weeks = request.form.get('max_weeks')
        
        # 基础查询
        query = """
            SELECT 
                pi.name as '姓名',
                pi.age as '年龄',
                pi.id_card as '身份证号',
                pi.outpatient_number as '门诊号',
                pi.phone as '联系电话',
                pi.address as '地址',
                pi.husband_name as '丈夫姓名',
                DATE_FORMAT(pi.due_date, '%Y-%m-%d') as '预产期',
                DATE_FORMAT(pi.last_menstrual_date, '%Y-%m-%d') as '末次月经',
                FLOOR(DATEDIFF(CURDATE(), pi.last_menstrual_date) / 7) as '当前孕周',
                DATE_FORMAT(pi.first_visit_date, '%Y-%m-%d') as '首次产检日期',
                DATE_FORMAT(pi.registration_date, '%Y-%m-%d') as '登记日期',
                pi.doctor_in_charge as '主管医生',
                pi.management_org as '管理机构',
                pi.pregnancy_count as '怀孕次数',
                pi.delivery_count as '分娩次数',
                pi.is_high_risk as '是否高危',
                COALESCE(cr.risk_factors, pi.risk_factors) as '高危因素',
                IF(pi.is_vip, '是', '否') as 'VIP状态',
                pi.has_given_birth as '分娩状态',
                DATE_FORMAT(cr.checkup_date, '%Y-%m-%d') as '产检日期',
                DATE_FORMAT(cr.appointment_date, '%Y-%m-%d') as '下次预约日期',
                cr.risk_color as '风险等级',
                IF(cr.fundal_height IS NOT NULL, CONCAT(cr.fundal_height, ' cm'), '') as '宫高(cm)',
                IF(cr.abdominal_circumference IS NOT NULL, CONCAT(cr.abdominal_circumference, ' cm'), '') as '腹围(cm)',
                cr.blood_pressure as '血压(mmHg)',
                IF(cr.weight IS NOT NULL, CONCAT(cr.weight, ' kg'), '') as '体重(kg)',
                cr.fetal_heart_rate as '胎心(次/分)',
                cr.notes as '备注',
                cr.doctor as '产检医生'
            FROM patient_info pi
            LEFT JOIN checkup_records cr ON pi.id = cr.patient_id
            WHERE 1=1
        """
        
        params = []
        
        # 添加日期范围筛选
        if start_date and end_date:
            query += " AND cr.checkup_date BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        
        # 添加孕周范围筛选
        if min_weeks and max_weeks:
            query += """
                AND DATEDIFF(CURDATE(), pi.last_menstrual_date) / 7 BETWEEN %s AND %s
            """
            params.extend([float(min_weeks), float(max_weeks)])
        elif min_weeks:
            query += """
                AND DATEDIFF(CURDATE(), pi.last_menstrual_date) / 7 >= %s
            """
            params.append(float(min_weeks))
        elif max_weeks:
            query += """
                AND DATEDIFF(CURDATE(), pi.last_menstrual_date) / 7 <= %s
            """
            params.append(float(max_weeks))
        
        # 添加其他搜索条件
        if request.form.get('name'):
            query += " AND pi.name LIKE %s"
            params.append(f"%{request.form.get('name')}%")
            
        if request.form.get('phone'):
            query += " AND pi.phone LIKE %s"
            params.append(f"%{request.form.get('phone')}%")
            
        if request.form.get('outpatient_number'):
            query += " AND pi.outpatient_number LIKE %s"
            params.append(f"%{request.form.get('outpatient_number')}%")
            
        if request.form.get('is_high_risk'):
            query += " AND pi.is_high_risk = %s"
            params.append(request.form.get('is_high_risk'))
        
        # 添加排序
        query += " ORDER BY pi.name, cr.checkup_date DESC"
        
        cursor.execute(query, params)
        records = cursor.fetchall()
        
        if not records:
            flash('没有找到符合条件的记录')
            return redirect(url_for('export_records'))
        
        # 创建Excel文件
        output = BytesIO()
        df = pd.DataFrame(records)
        
        # 使用openpyxl引擎写入Excel
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            sheet_name = '产检记录' if start_date and end_date else '孕产妇记录'
            df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 获取工作表
            worksheet = writer.sheets[sheet_name]
            
            # 调整列宽
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(str(col))
                )
                column_letter = get_column_letter(idx + 1)
                worksheet.column_dimensions[column_letter].width = min(max_length + 2, 50)
        
        # 将指针移到文件开头
        output.seek(0)
        
        # 生成文件名
        filename = f'孕产妇记录_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        if start_date and end_date:
            filename = f'产检记录_{start_date}至{end_date}.xlsx'
        if min_weeks or max_weeks:
            weeks_range = ''
            if min_weeks and max_weeks:
                weeks_range = f'{min_weeks}至{max_weeks}周'
            elif min_weeks:
                weeks_range = f'{min_weeks}周以上'
            elif max_weeks:
                weeks_range = f'{max_weeks}周以下'
            filename = f'孕产妇记录_{weeks_range}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        flash(f'导出失败：{str(e)}')
        return redirect(url_for('export_records'))
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/delete_patient/<int:patient_id>', methods=['POST'])
@login_required
def delete_patient(patient_id):
    try:
        # 首先删除孕产妇的所有产检记录
        conn = get_db_connection()
        if conn is None:
            flash('无法连接到数据库，请检查数据库配置')
            return redirect(url_for('index'))
        
        cursor = conn.cursor()
        cursor.execute('DELETE FROM checkup_records WHERE patient_id = %s', (patient_id,))
        conn.commit()
        
        # 然后删除孕产妇信息
        cursor.execute('DELETE FROM patient_info WHERE id = %s', (patient_id,))
        conn.commit()
        
        flash('孕产妇信息删除成功')
        return redirect(url_for('index'))
    except Exception as e:
        flash('删除失败：' + str(e))
        return redirect(url_for('patient_records', patient_id=patient_id))

@app.route('/edit_patient/<int:patient_id>', methods=['GET'])
@login_required
def edit_patient(patient_id):
    doctors = get_doctor_history()  # Fetch the list of doctors
    conn = get_db_connection()
    if conn is None:
        flash('无法连接到数据库，请检查数据库配置')
        return redirect(url_for('index'))
    
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute('SELECT * FROM patient_info WHERE id = %s', (patient_id,))
        patient = cursor.fetchone()
        
        if patient is None:
            flash('未找到孕产妇信息')
            return redirect(url_for('index'))
        
        # 获取所有主管医生历史记录
        cursor.execute('SELECT doctor_name FROM doctor_history')
        doctor_history = cursor.fetchall()

        # 获取当前日期
        today = datetime.now().strftime('%Y-%m-%d')

        return render_template('edit_patient.html', patient=patient, doctor_history=doctor_history, today=today)
    except Exception as e:
        flash('获取孕产妇信息失败：' + str(e))
        return redirect(url_for('index'))
    finally:
        cursor.close()
        conn.close()

@app.route('/edit_checkup/<int:record_id>', methods=['GET', 'POST'])
@login_required
def edit_checkup(record_id):
    conn = get_db_connection()
    if conn is None:
        flash('无法连接到数据库，请检查数据库配置')
        return redirect(url_for('index'))
        
    cursor = conn.cursor(dictionary=True)
    try:
        if request.method == 'GET':
            # 获取产检记录和孕产妇信息
            cursor.execute("""
                SELECT cr.*, pi.name as patient_name, pi.age, pi.phone, pi.outpatient_number
                FROM checkup_records cr 
                JOIN patient_info pi ON cr.patient_id = pi.id 
                WHERE cr.record_id = %s
            """, (record_id,))
            record = cursor.fetchone()
            
            if not record:
                flash('记录不存在')
                return redirect(url_for('index'))
                
            return render_template('edit_checkup.html', record=record)
            
        elif request.method == 'POST':
            # 更新产检记录
            doctor = request.form.get('doctor')
            risk_factors = request.form.get('risk_factors')
            risk_color = request.form.get('risk_color')
            checkup_date = request.form.get('checkup_date')
            appointment_date = request.form.get('appointment_date')
            fundal_height = request.form.get('fundal_height')
            abdominal_circumference = request.form.get('abdominal_circumference')
            blood_pressure = request.form.get('blood_pressure')
            weight = request.form.get('weight')
            fetal_heart_rate = request.form.get('fetal_heart_rate')
            notes = request.form.get('notes')

            # 检查下次预约时间是否填写
            if not appointment_date:
                flash('下次预约时间不能为空！')
                return redirect(url_for('edit_checkup', record_id=record_id))

            # 确保所有字段都被正确处理
            sql = """UPDATE checkup_records SET 
                doctor = %s,
                risk_factors = %s,
                risk_color = %s,
                checkup_date = %s,
                appointment_date = %s,
                fundal_height = %s,
                abdominal_circumference = %s,
                blood_pressure = %s,
                weight = %s,
                fetal_heart_rate = %s,
                notes = %s
                WHERE record_id = %s"""
                
            values = (
                doctor,
                risk_factors,
                risk_color,
                checkup_date,
                appointment_date,
                float(fundal_height) if fundal_height else None,  # 确保为浮点数
                float(abdominal_circumference) if abdominal_circumference else None,  # 确保为浮点数
                blood_pressure,
                float(weight) if weight else None,  # 确保为浮点数
                int(fetal_heart_rate) if fetal_heart_rate and isinstance(fetal_heart_rate, (int, float, str)) else None,  # 确保为整数
                notes,
                record_id
            )
            
            cursor.execute(sql, values)
            conn.commit()
            flash('产检记录更新成功！')
            
            # 获取patient_id用于重定向
            cursor.execute("SELECT patient_id FROM checkup_records WHERE record_id = %s", (record_id,))
            patient_id = cursor.fetchone()
            patient_id = patient_id['patient_id'] if patient_id else None  # Ensure patient_id is extracted correctly
            return redirect(url_for('patient_records', patient_id=patient_id))
            
    except Exception as e:
        conn.rollback()
        flash(f'错误：{str(e)}')
        return redirect(url_for('checkup_detail', record_id=record_id))
    finally:
        cursor.close()
        conn.close()

@app.route('/delete_checkup/<int:record_id>', methods=['POST'])
def delete_checkup(record_id):
    conn = get_db_connection()
    if conn is None:
        flash('无法连接到数据库，请检查数据库配置')
        return redirect(url_for('index'))
        
    cursor = conn.cursor(dictionary=True)
    try:
        # 获取patient_id用于重定向
        cursor.execute("SELECT patient_id FROM checkup_records WHERE record_id = %s", (record_id,))
        patient_id = cursor.fetchone()
        
        if not patient_id:
            flash('记录不存在')
            return redirect(url_for('index'))
        
        patient_id = patient_id['patient_id']
        
        # 删除产检记录
        sql = "DELETE FROM checkup_records WHERE record_id = %s"
        cursor.execute(sql, (record_id,))
        conn.commit()
        flash('产检记录删除成功！')
        
        return redirect(url_for('patient_records', patient_id=patient_id))
        
    except Exception as e:
        conn.rollback()
        flash(f'错误：{str(e)}')
        return redirect(url_for('checkup_detail', record_id=record_id))
    finally:
        cursor.close()
        conn.close()

@app.route('/get_doctors', methods=['GET'])
def get_doctors():
    conn = get_db_connection()
    if conn is None:
        return jsonify([])
        
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute("""
            SELECT doctor_name 
            FROM doctor_history 
            ORDER BY created_at DESC
        """)
        doctors = cursor.fetchall()
        return jsonify([doctor['doctor_name'] for doctor in doctors])
    except Exception as e:
        print(f'获取医生列表错误：{str(e)}')
        return jsonify([])
    finally:
        cursor.close()
        conn.close()

@app.route('/delete_doctor/<int:doctor_id>', methods=['POST'])
def delete_doctor(doctor_id):
    conn = get_db_connection()
    if conn is None:
        return jsonify({'success': False, 'message': '数据库连接失败'})
        
    cursor = conn.cursor()
    try:
        cursor.execute('DELETE FROM doctor_history WHERE id = %s', (doctor_id,))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': str(e)})
    finally:
        cursor.close()
        conn.close()

@app.route('/update_notification_status', methods=['POST'])
def update_notification_status():
    try:
        data = request.get_json()
        record_id = data.get('record_id')
        status = data.get('status')
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 更新通知状态
        cursor.execute("""
            UPDATE checkup_records 
            SET notification_status = %s 
            WHERE record_id = %s
        """, (status, record_id))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/export_template')
def export_template():
    try:
        template_data = {
            '姓名': ['张三'],
            '年龄': [28],
            '身份证号': ['430123199001011234'],
            '门诊号': ['MZ12345'],
            '联系电话': ['13800138000'],
            '地址': ['涟源市xxx街道'],
            '丈夫姓名': ['李四'],
            '预产期': ['2024-09-01'],
            '末次月经': ['2023-12-25'],
            '首次产检日期': ['2024-01-15'],
            '登记日期': ['2024-01-15'],
            '主管医生': ['王医生'],
            '管理机构': ['涟源市妇幼保健院'],
            '怀孕次数': [1],
            '分娩次数': [0],
            '是否高危': ['否'],
            '高危因素': [''],
            '是否VIP': ['否'],
            '分娩状态': ['在孕'],  # 添加分娩状态字段
        }
        
        # 创建DataFrame
        df = pd.DataFrame(template_data)
        
        # 创建一个BytesIO对象
        excel_file = BytesIO()
        
        # 使用openpyxl引擎写入Excel文件
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='导入模板', index=False)
            
            # 获取工作表
            worksheet = writer.sheets['导入模板']
            
            # 调整列宽
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(str(col))
                )
                column_letter = get_column_letter(idx + 1)
                worksheet.column_dimensions[column_letter].width = min(max_length + 2, 50)
        
        # 将指针移到文件开头
        excel_file.seek(0)
        
        return send_file(
            excel_file,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='孕产妇信息导入模板.xlsx'
        )
        
    except Exception as e:
        flash(f'导出模板失败：{str(e)}')
        return redirect(url_for('import_patients'))

# 用户注册页面
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        if not username or not password or not confirm_password:
            flash('所有字段都是必填的')
            return redirect(url_for('register'))
            
        if password != confirm_password:
            flash('密码不匹配')
            return redirect(url_for('register'))
            
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 使用哈希算法存储密码
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            cursor.execute("INSERT INTO users (username, password) VALUES (%s, %s)", (username, hashed_password))
            conn.commit()
            flash('注册成功，您可以登录了！', 'success')
            
            return redirect(url_for('login'))
        except pymysql.err.IntegrityError:
            flash('用户名已存在，请选择其他用户名')
        except Exception as e:
            flash(f'注册失败: {str(e)}')
        finally:
            cursor.close()
            conn.close()
            
    return render_template('register.html')

# 用户登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('所有字段都是必填的')
            return redirect(url_for('login'))
            
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            cursor.execute("SELECT * FROM users WHERE username = %s AND password = %s", (username, hashed_password))
            user = cursor.fetchone()
            
            if user:
                session['user_id'] = user['id']
                session['username'] = user['username']
                flash(f'欢迎回来，{user["username"]}!', 'success')
                return redirect(url_for('index'))
            else:
                flash('用户名或密码错误')
        except Exception as e:
            flash(f'登录失败: {str(e)}')
        finally:
            cursor.close()
            conn.close()
            
    return render_template('login.html')

# 用户登出路由
@app.route('/logout')
def logout():
    session.pop('user_id', None)
    session.pop('username', None)
    flash('您已成功登出', 'success')
    return redirect(url_for('index'))


# 登录后首页
@app.route('/')
@login_required
def index():
    return render_template('index.html', username=session['username'])

@app.route('/todays_entries', methods=['GET'])
@login_required
def todays_entries():
    conn = get_db_connection()
    if conn is None:
        flash('无法连接到数据库，请检查数据库配置')
        return redirect(url_for('index'))
        
    cursor = conn.cursor(dictionary=True)
    try:
        # 获取查询日期，默认为今天
        today = datetime.now().date()
        query_date = request.args.get('date', today.strftime('%Y-%m-%d'))

        # 查询指定日期新增的孕产妇信息
        cursor.execute("""
            SELECT 
                p.*,
                DATE(p.created_at) as entry_date
            FROM patient_info p
            WHERE DATE(p.created_at) = %s
            ORDER BY p.created_at DESC
        """, (query_date,))
        
        patients = cursor.fetchall()

        # 查询指定日期的产检记录
        cursor.execute("""
            SELECT 
                cr.*,
                pi.name,
                DATE(cr.created_at) as entry_date,
                pi.outpatient_number,
                pi.phone
            FROM checkup_records cr
            JOIN patient_info pi ON cr.patient_id = pi.id
            WHERE DATE(cr.created_at) = %s
            ORDER BY cr.created_at DESC
        """, (query_date,))
        
        checkups = cursor.fetchall()

        # 计算统计数据
        total_patients = len(patients)
        total_checkups = len(checkups)
        high_risk_count = sum(1 for p in patients if p.get('is_high_risk') == '高危')
        birth_count = sum(1 for p in patients if p.get('has_given_birth') == '已分娩')

        return render_template('todays_entries.html',
                             today=today.strftime('%Y-%m-%d'),
                             query_date=query_date,
                             patients=patients,
                             checkups=checkups,
                             total_patients=total_patients,
                             total_checkups=total_checkups,
                             high_risk_count=high_risk_count,
                             birth_count=birth_count)

    except Exception as e:
        error_message = f'错误：{str(e)}'
        return render_template('todays_entries.html', error=error_message)
    finally:
        cursor.close()
        conn.close()

@app.route('/get_risk_factors', methods=['GET'])
def get_risk_factors():
    search_term = request.args.get('term', '')
    conn = get_db_connection()
    if conn is None:
        return jsonify([])
        
    cursor = conn.cursor(dictionary=True)
    try:
        # 从产检记录和病人信息中获取不重复的高危因素
        cursor.execute("""
            SELECT DISTINCT risk_factors 
            FROM (
                SELECT risk_factors FROM checkup_records 
                WHERE risk_factors LIKE %s AND risk_factors != ''
                UNION
                SELECT risk_factors FROM patient_info 
                WHERE risk_factors LIKE %s AND risk_factors != ''
            ) as combined_factors
            ORDER BY risk_factors
            LIMIT 10
        """, (f'%{search_term}%', f'%{search_term}%'))
        
        results = cursor.fetchall()
        return jsonify([item['risk_factors'] for item in results])
    except Exception as e:
        print(f"Error fetching risk factors: {str(e)}")
        return jsonify([])
    finally:
        cursor.close()
        conn.close()

@app.route('/export_overdue_appointments', methods=['GET'])
def export_overdue_appointments():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取选中的医生
        selected_doctor = request.args.get('doctor')
        
        # 构建查询条件
        base_conditions = """
            WHERE cr.appointment_date <= CURDATE()
            AND cr.appointment_date >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
            AND pi.has_given_birth = '在孕'
            AND cr.checkup_date = (
                SELECT MAX(checkup_date) 
                FROM checkup_records 
                WHERE patient_id = cr.patient_id
            )
            AND cr.record_id = (
                SELECT MAX(record_id)
                FROM checkup_records
                WHERE patient_id = cr.patient_id
            )
            AND cr.notification_status = 0
        """
        
        if selected_doctor:
            base_conditions += " AND cr.doctor = %s"
            params = [selected_doctor]
        else:
            params = []
            
        # 获取已过期预约数据
        cursor.execute(f"""
            SELECT 
                pi.name as '姓名',
                pi.outpatient_number as '门诊病历号',
                pi.phone as '联系电话',
                DATE_FORMAT(cr.checkup_date, '%Y-%m-%d') as '上次产检日期',
                DATE_FORMAT(cr.appointment_date, '%Y-%m-%d') as '预约日期',
                cr.risk_color as '风险等级',
                cr.doctor as '医生'
            FROM checkup_records cr
            JOIN patient_info pi ON cr.patient_id = pi.id
            {base_conditions}
            ORDER BY cr.doctor ASC, cr.appointment_date DESC
        """, params)
        
        records = cursor.fetchall()
        
        if not records:
            flash('没有找到符合条件的记录')
            return redirect(url_for('appointments'))
            
        # 创建Excel文件
        output = BytesIO()
        df = pd.DataFrame(records)
        
        # 使用openpyxl引擎写入Excel
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='已过期预约', index=False)
            
            # 获取工作表
            worksheet = writer.sheets['已过期预约']
            
            # 调整列宽
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(str(col))
                )
                column_letter = get_column_letter(idx + 1)
                worksheet.column_dimensions[column_letter].width = min(max_length + 2, 50)
        
        # 将指针移到文件开头
        output.seek(0)
        
        # 生成文件名
        filename = f'已过期预约_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        if selected_doctor:
            filename = f'已过期预约_{selected_doctor}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        flash(f'导出失败：{str(e)}')
        return redirect(url_for('appointments'))
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# 在应用启动时初始化数据库
if __name__ == '__main__':
    init_db()
    app.run(debug=True) 