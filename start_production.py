#!/usr/bin/env python3
"""
生产环境启动脚本
"""
import os
import sys

def main():
    # 设置生产环境
    os.environ['FLASK_CONFIG'] = 'production'
    os.environ['FLASK_DEBUG'] = 'false'
    
    try:
        from app import app
        
        print("=" * 60)
        print("🏥 孕产妇管理系统 - 生产环境")
        print("=" * 60)
        print("🌐 域名: https://pm.beimoyinhenlinlin.cn/")
        print("🖥️  服务器: 110.41.38.190")
        print("🗄️  数据库: MySQL")
        print("⚙️  模式: 生产环境")
        print("=" * 60)
        
        # 生产环境使用Gunicorn启动
        print("🚀 使用Gunicorn启动生产服务器...")
        print("📍 内部地址: http://127.0.0.1:8000")
        print("🌐 外部访问: https://pm.beimoyinhenlinlin.cn/")
        print("\n按 Ctrl+C 停止服务器\n")
        
        # 启动Gunicorn
        import subprocess
        cmd = [
            'gunicorn',
            '--bind', '0.0.0.0:8000',
            '--workers', '4',
            '--worker-class', 'sync',
            '--timeout', '30',
            '--keepalive', '2',
            '--max-requests', '1000',
            '--preload',
            'app:app'
        ]
        
        subprocess.run(cmd)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
