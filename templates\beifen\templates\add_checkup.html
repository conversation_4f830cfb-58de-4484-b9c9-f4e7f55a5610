<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加产检记录</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        window.onload = function() {
            {% if error %}
                alert("{{ error|escape }}");
            {% endif %}
        };
    </script>
    <style>
        /* 自动完成下拉框样式 */
        .autocomplete-container {
            position: relative;
            width: 100%;
        }
        
        .autocomplete-dropdown {
            position: absolute;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }
        
        .autocomplete-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .autocomplete-item:hover {
            background-color: #f8f9fa;
        }
        
        .common-factors {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
        }
        
        .common-factor {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 4px 8px;
            margin-right: 5px;
            margin-bottom: 5px;
            cursor: pointer;
        }
        
        .common-factor:hover {
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button"><i class="fas fa-home"></i> 返回首页</a>
        <a href="{{ url_for('patient_records', patient_id=patient_id) }}" class="button"><i class="fas fa-arrow-left"></i> 返回孕产妇记录</a>
    </div>

    <div class="form-container">
        <!-- 孕产妇信息摘要 -->
        <div class="patient-summary">
            <h2><i class="fas fa-user"></i> 孕产妇信息</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <label>姓名：</label>
                    <span>{{ patient.name }}</span>
                </div>
                <div class="summary-item">
                    <label>年龄：</label>
                    <span>{{ patient.age }}岁</span>
                </div>
                <div class="summary-item">
                    <label>门诊号：</label>
                    <span>{{ patient.outpatient_number }}</span>
                </div>
                <div class="summary-item">
                    <label>联系电话：</label>
                    <span>{{ patient.phone }}</span>
                </div>
            </div>
        </div>

        <form method="post" class="checkup-form">
            <!-- 就诊信息 -->
            <div class="form-section">
                <h2><i class="fas fa-stethoscope"></i> 就诊信息</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="checkup_date">就诊日期</label>
                        <input type="date" id="checkup_date" name="checkup_date" value="{{ default_data.checkup_date }}" required>
                    </div>
                    <div class="form-group">
                        <label for="doctor">主治医生</label>
                        <select id="doctor" name="doctor" required>
                            <option value="">请选择医生</option>
                            <option value="{{ default_data.doctor }}" selected>{{ default_data.doctor }}</option>
                        </select>
                    </div>
                    <script>
                        fetch('/get_doctors')
                            .then(response => response.json())
                            .then(doctors => {
                                const select = document.getElementById('doctor');
                                doctors.forEach(doctorName => {
                                    const option = document.createElement('option');
                                    option.value = doctorName;
                                    option.textContent = doctorName;
                                    select.appendChild(option);
                                });
                            })
                            .catch(error => console.error('获取医生列表失败:', error));
                    </script>
                    <div class="form-group">
                        <label for="appointment_date">下次预约日期</label>
                        <input type="date" id="appointment_date" name="appointment_date">
                    </div>
                </div>
            </div>

            <!-- 风险评估 -->
            <div class="form-section">
                <h2><i class="fas fa-exclamation-triangle"></i> 风险评估</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="risk_color">高危等级</label>
                        <select id="risk_color" name="risk_color">
                            <option value="">无</option>
                            <option value="yellow" {% if default_data.risk_color == 'yellow' %}selected{% endif %}>黄色</option>
                            <option value="orange" {% if default_data.risk_color == 'orange' %}selected{% endif %}>橙色</option>
                            <option value="red" {% if default_data.risk_color == 'red' %}selected{% endif %}>红色</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="risk_factors">高危因素</label>
                        <div class="autocomplete-container">
                            <input type="text" id="risk_factors" name="risk_factors" value="{{ default_data.risk_factors }}">
                            <div id="risk-autocomplete" class="autocomplete-dropdown"></div>
                        </div>
                        <div class="common-factors" id="common-factors">
                            <!-- 这里会通过 JavaScript 添加常用高危因素按钮 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 体征数据 -->
            <div class="form-section">
                <h2><i class="fas fa-heartbeat"></i> 体征数据</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="fundal_height">宫底高度(cm)</label>
                        <input type="number" id="fundal_height" name="fundal_height" step="0.1" value="{{ default_data.fundal_height }}">
                    </div>
                    <div class="form-group">
                        <label for="abdominal_circumference">腹围(cm)</label>
                        <input type="number" id="abdominal_circumference" name="abdominal_circumference" step="0.1" value="{{ default_data.abdominal_circumference }}">
                    </div>
                    <div class="form-group">
                        <label for="blood_pressure">血压(mmHg)</label>
                        <input type="text" id="blood_pressure" name="blood_pressure" placeholder="如：120/80" value="{{ default_data.blood_pressure }}">
                    </div>
                    <div class="form-group">
                        <label for="weight">体重(kg)</label>
                        <input type="number" id="weight" name="weight" step="0.1" value="{{ default_data.weight }}">
                    </div>
                    <div class="form-group">
                        <label for="fetal_heart_rate">胎心率(次/分)</label>
                        <input type="text" id="fetal_heart_rate" name="fetal_heart_rate" value="{{ default_data.fetal_heart_rate }}">
                    </div>
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="form-section">
                <h2><i class="fas fa-clipboard"></i> 备注信息</h2>
                <div class="form-group">
                    <textarea id="notes" name="notes" rows="4" placeholder="请输入备注信息..."></textarea>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="submit-btn">
                    <i class="fas fa-save"></i> 保存记录
                </button>
            </div>
        </form>
    </div>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <script>
                alert("{{ messages[0] }}");  // 弹出提示信息
            </script>
        {% endif %}
    {% endwith %}

    <style>
        .doctor-input-group {
            display: flex;
            gap: var(--spacing-unit);
            align-items: center;
        }
        
        .doctor-input-group input {
            flex: 1;
        }
        
        .doctor-input-group .button {
            padding: calc(var(--spacing-unit) * 1.5);
            height: 100%;
        }
    </style>

    <script>
        // 设置就诊日期默认为当天
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            const todayStr = `${year}-${month}-${day}`;
            document.getElementById('checkup_date').value = todayStr;

            // 获取主管医生历史记录
            fetch('/get_doctors')
                .then(response => response.json())
                .then(doctors => {
                    const datalist = document.getElementById('doctor-list');
                    doctors.forEach(doctorName => {
                        const option = document.createElement('option');
                        option.value = doctorName;
                        datalist.appendChild(option);
                    });
                })
                .catch(error => console.error('获取医生列表失败:', error));
        });

        function calculatePregnancyWeekAndDueDate() {
            // ... existing code ...
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 找到高危因素输入框和下拉容器
            const riskFactorsInput = document.getElementById('risk_factors');
            const autocompleteDropdown = document.getElementById('risk-autocomplete');
            const commonFactorsContainer = document.getElementById('common-factors');
            
            // 定义常见高危因素
            const commonRiskFactors = [
                "高龄产妇", 
                "妊娠期糖尿病", 
                "妊娠期高血压", 
                "多胎妊娠"
            ];
            
            // 添加常见高危因素按钮
            commonRiskFactors.forEach(factor => {
                const btn = document.createElement('span');
                btn.className = 'common-factor';
                btn.textContent = factor;
                btn.addEventListener('click', function() {
                    addRiskFactor(factor);
                });
                commonFactorsContainer.appendChild(btn);
            });
            
            // 函数：添加高危因素到输入框
            function addRiskFactor(factor) {
                const currentValue = riskFactorsInput.value.trim();
                if (!currentValue) {
                    riskFactorsInput.value = factor;
                } else if (!currentValue.includes(factor)) {
                    riskFactorsInput.value = currentValue + "、" + factor;
                }
            }
            
            // 函数：获取高危因素建议
            async function fetchRiskFactors(term) {
                try {
                    const response = await fetch(`/get_risk_factors?term=${encodeURIComponent(term)}`);
                    if (response.ok) {
                        return await response.json();
                    }
                    return [];
                } catch (error) {
                    console.error('获取高危因素失败:', error);
                    return [];
                }
            }
            
            // 防抖函数
            let debounceTimer;
            function debounce(func, delay) {
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => func.apply(context, args), delay);
                };
            }
            
            // 处理输入事件
            riskFactorsInput.addEventListener('input', debounce(async function() {
                const term = this.value.trim();
                if (term.length < 1) {
                    autocompleteDropdown.style.display = 'none';
                    return;
                }
                
                const suggestions = await fetchRiskFactors(term);
                
                if (suggestions.length === 0) {
                    autocompleteDropdown.style.display = 'none';
                    return;
                }
                
                // 显示建议
                autocompleteDropdown.innerHTML = '';
                suggestions.forEach(suggestion => {
                    const item = document.createElement('div');
                    item.className = 'autocomplete-item';
                    item.textContent = suggestion;
                    item.addEventListener('click', function() {
                        riskFactorsInput.value = suggestion;
                        autocompleteDropdown.style.display = 'none';
                    });
                    autocompleteDropdown.appendChild(item);
                });
                
                autocompleteDropdown.style.display = 'block';
            }, 300));
            
            // 点击文档其他地方时隐藏下拉菜单
            document.addEventListener('click', function(e) {
                if (!riskFactorsInput.contains(e.target) && !autocompleteDropdown.contains(e.target)) {
                    autocompleteDropdown.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html> 