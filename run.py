#!/usr/bin/env python3
"""
孕产妇管理系统启动脚本
"""
import os
from app import app, init_db

if __name__ == '__main__':
    # 初始化数据库
    init_db()
    
    # 获取环境变量
    host = os.environ.get('HOST', '127.0.0.1')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    print(f"启动孕产妇管理系统...")
    print(f"访问地址: http://{host}:{port}")
    print(f"调试模式: {debug}")
    
    # 启动应用
    app.run(host=host, port=port, debug=debug)
