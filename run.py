#!/usr/bin/env python3
"""
孕产妇管理系统启动脚本
"""
import os
import sys

def main():
    try:
        # 导入应用
        from app import app, init_db

        # 初始化数据库
        print("正在初始化数据库...")
        init_db()

        # 获取环境变量
        host = os.environ.get('HOST', '127.0.0.1')
        port = int(os.environ.get('PORT', 5000))
        debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

        print("=" * 50)
        print("🏥 孕产妇管理系统")
        print("=" * 50)
        print(f"📍 访问地址: http://{host}:{port}")
        print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
        print(f"🐍 Python版本: {sys.version}")
        print("=" * 50)
        print("按 Ctrl+C 停止服务器")
        print()

        # 启动应用
        app.run(host=host, port=port, debug=debug)

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
