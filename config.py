import mysql.connector
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_connection():
    max_retries = 3
    retry_delay = 2  # 秒
    
    for attempt in range(max_retries):
        try:
            logger.info(f"尝试连接数据库 (尝试 {attempt + 1}/{max_retries})")
            connection = mysql.connector.connect(
                host='*************',
                user='root',
                password='windows1',
                port=3306,
                database='pregnancy_management',
                connect_timeout=10,  # 连接超时时间
                charset='utf8mb4',   # 支持中文字符
                collation='utf8mb4_unicode_ci'
            )
            
            # 测试连接
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            
            logger.info("数据库连接成功！")
            return connection
            
        except mysql.connector.Error as error:
            error_msg = str(error)
            logger.error(f"连接数据库失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
            
            if "Unknown database" in error_msg:
                try:
                    logger.info("尝试创建数据库和表")
                    temp_conn = mysql.connector.connect(
                        host='*************',
                        user='root',
                        password='windows1',
                        port=3306
                    )
                    temp_cursor = temp_conn.cursor()
                    
                    # 创建数据库
                    temp_cursor.execute("CREATE DATABASE IF NOT EXISTS pregnancy_management")
                    temp_cursor.execute("USE pregnancy_management")
                    
                    # 创建表
                    temp_cursor.execute("""
                        CREATE TABLE IF NOT EXISTS patient_info (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            name VARCHAR(50) NOT NULL,
                            age INT NOT NULL,
                            id_card VARCHAR(18) NOT NULL,
                            outpatient_number VARCHAR(50),
                            address VARCHAR(200) NOT NULL,
                            phone VARCHAR(20) NOT NULL,
                            husband_name VARCHAR(50),
                            due_date DATE,
                            last_menstrual_date DATE,
                            first_visit_date DATE,
                            registration_date DATE,
                            doctor_in_charge VARCHAR(50),
                            management_org VARCHAR(100) DEFAULT '涟源市妇幼保健院',
                            is_high_risk VARCHAR(10) DEFAULT '否',
                            is_vip BOOLEAN DEFAULT FALSE,
                            pregnancy_count INT DEFAULT 1,
                            delivery_count INT DEFAULT 0,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE KEY idx_phone (phone),
                            UNIQUE KEY idx_id_card (id_card),
                            INDEX idx_outpatient_number (outpatient_number)
                        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
                    """)
                    
                    temp_cursor.execute("""
                        CREATE TABLE IF NOT EXISTS checkup_records (
                            record_id INT AUTO_INCREMENT PRIMARY KEY,
                            patient_id INT NOT NULL,
                            outpatient_number VARCHAR(50),
                            doctor VARCHAR(50),
                            risk_factors TEXT,
                            risk_color VARCHAR(20),
                            checkup_date DATE,
                            appointment_date DATE,
                            fundal_height DECIMAL(5,2),
                            abdominal_circumference DECIMAL(5,2),
                            blood_pressure VARCHAR(20),
                            weight DECIMAL(5,2),
                            fetal_heart_rate INT,
                            notes TEXT,
                            notification_status BOOLEAN DEFAULT FALSE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (patient_id) REFERENCES patient_info(id),
                            INDEX idx_checkup_date (checkup_date),
                            INDEX idx_appointment_date (appointment_date),
                            INDEX idx_outpatient_number (outpatient_number)
                        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
                    """)

                    temp_cursor.execute("""
                        CREATE TABLE IF NOT EXISTS doctor_history (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            doctor_name VARCHAR(50) UNIQUE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
                    """)
                    
                    temp_cursor.execute("""
                        CREATE TABLE IF NOT EXISTS users (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            username VARCHAR(50) NOT NULL UNIQUE,
                            password VARCHAR(255) NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
                    """)

                    temp_conn.commit()
                    logger.info("数据库和表创建成功！")
                    
                except Exception as create_error:
                    logger.error(f"创建数据库失败: {str(create_error)}")
                finally:
                    if 'temp_cursor' in locals():
                        temp_cursor.close()
                    if 'temp_conn' in locals():
                        temp_conn.close()
            
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            continue
            
    logger.error("达到最大重试次数，无法连接到数据库")
    return None 