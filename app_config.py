# 应用配置文件
import os

class Config:
    """应用基础配置"""
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your_secret_key_change_in_production'
    
    # 文件上传配置
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
    
    # 数据库配置
    DB_HOST = os.environ.get('DB_HOST') or '************'
    DB_USER = os.environ.get('DB_USER') or 'root'
    DB_PASSWORD = os.environ.get('DB_PASSWORD') or '123456'
    DB_PORT = int(os.environ.get('DB_PORT') or 3306)
    DB_NAME = os.environ.get('DB_NAME') or 'pregnancy_management'
    
    # 应用配置
    DEBUG = os.environ.get('FLASK_DEBUG') or False
    
    # 分页配置
    RECORDS_PER_PAGE = 20
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = 'app.log'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY')
    
    # 生产环境应该使用环境变量
    if not SECRET_KEY:
        raise ValueError("生产环境必须设置SECRET_KEY环境变量")

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DB_NAME = 'pregnancy_management_test'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
