# 孕产妇管理系统

## 项目概述
这是一个基于Flask的孕产妇管理系统，用于管理孕产妇信息、产检记录、预约管理等功能。

## 主要功能
1. **用户管理**
   - 用户注册/登录
   - 会话管理

2. **孕产妇管理**
   - 添加孕产妇信息
   - 编辑孕产妇信息
   - 删除孕产妇信息
   - 搜索孕产妇

3. **产检记录管理**
   - 添加产检记录
   - 编辑产检记录
   - 删除产检记录
   - 查看产检详情

4. **预约管理**
   - 查看预约列表
   - 过期预约提醒
   - 即将到期预约

5. **数据管理**
   - Excel批量导入孕产妇信息
   - 导出产检记录
   - 导出模板下载

6. **医生管理**
   - 医生历史记录
   - 医生信息管理

## 项目结构
```
pregnancy_management/
├── app.py                 # 主应用文件
├── config.py             # 数据库配置
├── requirements.txt      # 依赖包列表
├── create_tables.sql     # 数据库表结构
├── static/              # 静态文件
│   ├── style.css        # 样式文件
│   └── template.xlsx    # Excel导入模板
├── templates/           # HTML模板
│   ├── index.html       # 首页
│   ├── login.html       # 登录页
│   ├── register.html    # 注册页
│   ├── add_patient.html # 添加孕产妇
│   ├── edit_patient.html# 编辑孕产妇
│   ├── patient_records.html # 孕产妇记录
│   ├── add_checkup.html # 添加产检
│   ├── edit_checkup.html# 编辑产检
│   ├── checkup_detail.html # 产检详情
│   ├── search_patient.html # 搜索孕产妇
│   ├── appointments.html# 预约管理
│   ├── import_patients.html # 批量导入
│   ├── export_records.html # 导出记录
│   ├── todays_entries.html # 今日录入
│   └── manage_doctors.html # 医生管理
├── uploads/             # 上传文件目录
└── logs/               # 日志目录
```

## 数据库表结构
1. **patient_info** - 孕产妇信息表
2. **checkup_records** - 产检记录表
3. **doctor_history** - 医生历史记录表
4. **users** - 用户表

## 安装和运行

### 环境要求
- Python 3.8+
- MySQL 5.7+

### 安装步骤
1. 克隆项目
```bash
git clone <repository-url>
cd pregnancy_management
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置数据库
- 修改 `config.py` 中的数据库连接信息
- 运行 `create_tables.sql` 创建数据库表

4. 运行应用
```bash
python app.py
```

## 主要优化内容

### 代码优化
1. **重复代码消除**
   - 提取了预产期计算函数 `calculate_due_date()`
   - 统一了错误处理机制
   - 优化了数据库连接管理

2. **安全性增强**
   - 为所有需要登录的路由添加了 `@login_required` 装饰器
   - 修复了配置文件中的密码不一致问题

3. **文件结构优化**
   - 删除了不必要的备份文件夹 `templates/beifen`
   - 删除了重复的SQL文件
   - 删除了空的配置文件

4. **依赖管理优化**
   - 清理了 `requirements.txt` 中的重复依赖
   - 移除了不必要的包

### 功能完善
1. **登录验证**
   - 所有主要功能都需要登录才能访问
   - 统一的会话管理

2. **数据验证**
   - 电话号码格式验证
   - 身份证号验证
   - 门诊号格式验证

3. **错误处理**
   - 统一的错误提示机制
   - 数据库事务管理

## 使用说明

### 首次使用
1. 访问系统首页
2. 注册新用户账号
3. 登录系统
4. 开始使用各项功能

### 批量导入
1. 下载Excel模板
2. 按模板格式填写数据
3. 上传Excel文件进行批量导入

### 数据导出
1. 设置筛选条件
2. 选择导出格式
3. 下载导出文件

## 注意事项
1. 确保数据库连接正常
2. 定期备份数据
3. 保护用户隐私信息
4. 及时更新系统依赖

## 技术栈
- **后端**: Flask, MySQL
- **前端**: HTML, CSS, JavaScript
- **数据处理**: Pandas, OpenPyXL
- **部署**: Gunicorn

## 维护建议
1. 定期检查日志文件
2. 监控数据库性能
3. 及时更新安全补丁
4. 备份重要数据
