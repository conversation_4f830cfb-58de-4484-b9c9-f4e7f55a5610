<!DOCTYPE html>
<html>
<head>
    <title>搜索孕产妇 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav">
        <a href="/" class="button secondary">
            <i class="fas fa-home"></i> 返回首页
        </a>
        <a href="{{ url_for('add_patient') }}" class="button">
            <i class="fas fa-user-plus"></i> 新增孕产妇
        </a>
    </div>

    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1><i class="fas fa-search"></i> 搜索孕产妇</h1>
            <p class="subtitle">快速查找孕产妇信息和管理产检记录</p>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-form">
                <form action="{{ url_for('search_patient') }}" method="POST">
                    <div class="search-input-group">
                        <input type="text" 
                               name="search_term" 
                               value="{{ search_term if search_term else '' }}" 
                               placeholder="输入姓名/门诊号/身份证号" 
                               required>
                        <button type="submit" class="search-button">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 搜索结果 -->
        {% if patients %}
        <div class="card">
            <div class="results-header">
                <h2><i class="fas fa-list"></i> 搜索结果</h2>
                <span class="results-count">共找到 {{ patients|length }} 条记录</span>
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th><i class="fas fa-user"></i> 姓名</th>
                            <th><i class="fas fa-hospital-user"></i> 门诊号</th>
                            <th><i class="fas fa-birthday-cake"></i> 年龄</th>
                            <th><i class="fas fa-phone"></i> 联系电话</th>
                            <th><i class="fas fa-tag"></i> 状态</th>
                            <th><i class="fas fa-calendar"></i> 预产期</th>
                            <th><i class="fas fa-cog"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for patient in patients %}
                        <tr>
                            <td>{{ patient.name }}</td>
                            <td>{{ patient.outpatient_number }}</td>
                            <td>{{ patient.age }}</td>
                            <td>{{ patient.phone }}</td>
                            <td>
                                {% if patient.risk_color %}
                                <span class="status-tag risk-{{ patient.risk_color }}">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    {{ patient.risk_color }}
                                </span>
                                {% endif %}
                                {% if patient.is_vip == 'true' %}
                                <span class="status-tag vip">
                                    <i class="fas fa-crown"></i> VIP
                                </span>
                                {% endif %}
                            </td>
                            <td>{{ patient.expected_delivery_date }}</td>
                            <td>
                                <a href="{{ url_for('patient_records', patient_id=patient.id) }}" class="action-button view">
                                    <i class="fas fa-folder-open"></i> 查看
                                </a>
                                <a href="{{ url_for('add_checkup', patient_id=patient.id) }}" class="action-button add">
                                    <i class="fas fa-plus"></i> 产检
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% elif search_term %}
        <div class="card">
            <div class="no-results">
                <i class="fas fa-exclamation-circle"></i>
                <h3>未找到匹配的孕产妇记录</h3>
                <p>请检查输入信息是否正确，或尝试使用其他搜索条件</p>
                <a href="{{ url_for('add_patient') }}" class="button">
                    <i class="fas fa-user-plus"></i> 新增孕产妇
                </a>
            </div>
        </div>
        {% endif %}

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                <div class="alert {{ category }}">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ message }}
                </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <style>
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: calc(var(--spacing-unit) * 3);
        }
        
        .results-header h2 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-unit);
        }
        
        .results-count {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .no-results {
            text-align: center;
            padding: calc(var(--spacing-unit) * 4);
        }
        
        .no-results i {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: calc(var(--spacing-unit) * 2);
        }
        
        .no-results h3 {
            margin: calc(var(--spacing-unit) * 2) 0;
            color: var(--text-primary);
        }
        
        .no-results p {
            color: var(--text-secondary);
            margin-bottom: calc(var(--spacing-unit) * 3);
        }
    </style>
</body>
</html> 