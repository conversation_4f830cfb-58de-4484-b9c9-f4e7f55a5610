<!DOCTYPE html>
<html>
<head>
    <title>产检记录详情 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button secondary">
            <i class="fas fa-home"></i> 返回首页
        </a>
        <a href="{{ url_for('patient_records', patient_id=record.patient_id) }}" class="button secondary">
            <i class="fas fa-arrow-left"></i> 返回孕产妇记录
        </a>
    </div>

    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-notes-medical"></i> 
                    产检记录详情
                    {% if record.risk_color %}
                    <span class="status-tag risk-{{ record.risk_color }}">
                        <i class="fas fa-exclamation-triangle"></i> {{ record.risk_color }}
                    </span>
                    {% endif %}
                </h1>
                <div class="detail-actions">
                    <a href="{{ url_for('edit_checkup', record_id=record.record_id) }}" class="button action-button">
                        <i class="fas fa-edit"></i> 编辑记录
                    </a>
                
                    <form action="{{ url_for('delete_checkup', record_id=record.record_id) }}" method="post" style="display:inline-block;">
                        <button type="submit" class="button action-button danger" onclick="return confirm('确定要删除这条产检记录吗？');">
                            <i class="fas fa-trash"></i> 删除记录
                        </button>
                    </form>
                </div>
                
            </div>
        </div>

        <!-- 卡片容器 -->
        <div class="cards-container">
            <!-- 孕产妇基本信息 -->
            <div class="card">
                <div class="detail-section">
                    <h2><i class="fas fa-user"></i> 孕产妇信息</h2>
                    <div class="detail-grid horizontal">
                        <div class="detail-item">
                            <label>姓名</label>
                            <span>{{ record.patient_name }}</span>
                        </div>
                        <div class="detail-item">
                            <label>年龄</label>
                            <span>{{ record.age }}岁</span>
                        </div>
                        <div class="detail-item">
                            <label>门诊病历号</label>
                            <span>{{ record.outpatient_number }}</span>
                        </div>
                        <div class="detail-item">
                            <label>联系电话</label>
                            <span>{{ record.phone }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 就诊信息 -->
            <div class="card">
                <div class="detail-section">
                    <h2><i class="fas fa-calendar-check"></i> 就诊信息</h2>
                    <div class="detail-grid horizontal">
                        <div class="detail-item">
                            <label>就诊日期</label>
                            <span>{{ record.checkup_date }}</span>
                        </div>
                        <div class="detail-item">
                            <label>主治医生</label>
                            <span>{{ record.doctor }}</span>
                        </div>
                        <div class="detail-item">
                            <label>下次预约日期</label>
                            <span>{{ record.appointment_date }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 风险评估 -->
            <div class="card">
                <div class="detail-section">
                    <h2><i class="fas fa-exclamation-triangle"></i> 风险评估</h2>
                    <div class="detail-grid horizontal">
                        <div class="detail-item">
                            <label>高危等级</label>
                            <span>
                                {% if record.risk_color %}
                                <span class="status-tag risk-{{ record.risk_color }}">
                                    {{ record.risk_color }}
                                </span>
                                {% else %}
                                <span class="status-tag">无风险</span>
                                {% endif %}
                            </span>
                        </div>
                        {% if record.risk_factors %}
                        <div class="detail-item flex-grow">
                            <label>高危因素</label>
                            <span>{{ record.risk_factors }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 体征数据 -->
            <div class="card">
                <div class="detail-section">
                    <h2><i class="fas fa-heartbeat"></i> 体征数据</h2>
                    <div class="detail-grid horizontal">
                        <div class="detail-item">
                            <label>宫底高度</label>
                            <span>{{ record.fundal_height }} cm</span>
                        </div>
                        <div class="detail-item">
                            <label>腹围</label>
                            <span>{{ record.abdominal_circumference }} cm</span>
                        </div>
                        <div class="detail-item">
                            <label>血压</label>
                            <span>{{ record.blood_pressure }} mmHg</span>
                        </div>
                        <div class="detail-item">
                            <label>体重</label>
                            <span>{{ record.weight }} kg</span>
                        </div>
                        <div class="detail-item">
                            <label>胎心率</label>
                            <span>{{ record.fetal_heart_rate }} 次/分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 备注信息 -->
        {% if record.notes %}
        <div class="card">
            <div class="detail-section">
                <h2><i class="fas fa-clipboard"></i> 备注信息</h2>
                <div class="notes-section">
                    <div class="notes-content">{{ record.notes }}</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                <div class="alert {{ category }}">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ message }}
                </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <style>
        .cards-container {
            display: flex;
            flex-wrap: wrap;
            gap: calc(var(--spacing-unit) * 2);
            margin-bottom: calc(var(--spacing-unit) * 2);
        }
        
        .card {
            flex: 1 1 calc(50% - var(--spacing-unit));
            min-width: 300px;
            margin-bottom: 0;
        }
        
        .detail-section {
            padding: calc(var(--spacing-unit) * 3) 0;
            border-bottom: 2px solid #1e88e5;
        }
        
        .detail-section:first-child {
            padding-top: 0;
        }
        
        .detail-section:last-child {
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .detail-grid.horizontal {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            padding-bottom: calc(var(--spacing-unit) * 2);
            gap: calc(var(--spacing-unit) * 4);
        }
        
        .detail-grid.horizontal .detail-item {
            flex: 0 0 auto;
            min-width: 150px;
        }
        
        .detail-grid.horizontal .detail-item.flex-grow {
            flex: 1 1 auto;
            min-width: 300px;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-unit);
        }
        
        .detail-item label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .detail-item span {
            color: var(--text-primary);
            font-size: 1rem;
        }
        
        .notes-section {
            background: var(--background-primary);
            padding: calc(var(--spacing-unit) * 2);
            border-radius: var(--border-radius);
        }
        
        .notes-content {
            color: var(--text-primary);
            line-height: 1.6;
            white-space: pre-wrap;
        }
        
        .action-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            font-size: 0.875rem;
            color: #fff;
            background-color: #1e88e5; /* 蓝色背景 */
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .action-button:hover {
            background-color: #1565c0; /* 悬停时的蓝色背景 */
        }

        .action-button.danger {
            background-color: #d32f2f; /* 红色背景 */
        }

        .action-button.danger:hover {
            background-color: #c62828; /* 悬停时的红色背景 */
        }

        /* 添加滚动条样式 */
        .detail-grid.horizontal::-webkit-scrollbar {
            height: 8px;
        }
        
        .detail-grid.horizontal::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .detail-grid.horizontal::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        .detail-grid.horizontal::-webkit-scrollbar-thumb:hover {
            background: #666;
        }
        
        @media (max-width: 768px) {
            .cards-container {
                flex-direction: column;
            }
            
            .card {
                flex: 1 1 100%;
                min-width: 0;
            }
            
            .detail-grid.horizontal {
                flex-wrap: wrap;
                overflow-x: visible;
                padding-bottom: 0;
            }
            
            .detail-grid.horizontal .detail-item,
            .detail-grid.horizontal .detail-item.flex-grow {
                flex: 1 1 calc(50% - var(--spacing-unit));
                min-width: 0;
            }
        }
    </style>
</body>
</html> 