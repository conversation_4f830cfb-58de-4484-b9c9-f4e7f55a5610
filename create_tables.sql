CREATE DATABASE IF NOT EXISTS pregnancy_management;
USE pregnancy_management;

-- 创建孕产妇信息表
CREATE TABLE IF NOT EXISTS patient_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    age INT NOT NULL,
    id_card VARCHAR(18) NOT NULL,
    outpatient_number VARCHAR(50),
    address VARCHAR(200) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    husband_name VARCHAR(50),
    due_date DATE,
    last_menstrual_date DATE,
    first_visit_date DATE,
    registration_date DATE,
    doctor_in_charge VARCHAR(50),
    management_org VARCHAR(100) DEFAULT '涟源市妇幼保健院',
    is_high_risk VARCHAR(10) DEFAULT '否',
    is_vip BOOLEAN DEFAULT FALSE,
    pregnancy_count INT DEFAULT 1,
    delivery_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY idx_phone (phone),
    UNIQUE KEY idx_id_card (id_card),
    INDEX idx_outpatient_number (outpatient_number)
);

-- 创建产检记录表
CREATE TABLE IF NOT EXISTS checkup_records (
    record_id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    outpatient_number VARCHAR(50),
    doctor VARCHAR(50),
    risk_factors TEXT,
    risk_color VARCHAR(20),
    checkup_date DATE,
    appointment_date DATE,
    fundal_height DECIMAL(5,2),
    abdominal_circumference DECIMAL(5,2),
    blood_pressure VARCHAR(20),
    weight DECIMAL(5,2),
    fetal_heart_rate INT,
    notes TEXT,
    notification_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patient_info(id),
    INDEX idx_checkup_date (checkup_date),
    INDEX idx_appointment_date (appointment_date),
    INDEX idx_outpatient_number (outpatient_number)
); 
-- 登录注册表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
