<!DOCTYPE html>
<html>
<head>
    <title>管理主管医生记录 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button secondary">
            <i class="fas fa-home"></i> 返回首页
        </a>
    </div>

    <div class="main-container">
        <div class="page-header">
            <h1><i class="fas fa-user-md"></i> 管理主管医生记录</h1>
        </div>

        <div class="card">
            <div class="doctor-list">
                <table>
                    <thead>
                        <tr>
                            <th>医生姓名</th>
                            <th>添加时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="doctorTableBody">
                        <!-- 医生列表将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <style>
        .doctor-list {
            margin-top: var(--spacing-unit);
        }
        
        .doctor-list table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .doctor-list th,
        .doctor-list td {
            padding: calc(var(--spacing-unit) * 2);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .doctor-list th {
            background-color: var(--background-secondary);
            font-weight: 500;
        }
        
        .delete-btn {
            color: var(--color-danger);
            background: none;
            border: none;
            cursor: pointer;
            padding: var(--spacing-unit);
            border-radius: var(--border-radius);
            transition: background-color 0.3s;
        }
        
        .delete-btn:hover {
            background-color: var(--background-danger);
        }
    </style>

    <script>
        // 加载医生列表
        function loadDoctors() {
            fetch('/get_doctors')
                .then(response => response.json())
                .then(doctors => {
                    const tbody = document.getElementById('doctorTableBody');
                    tbody.innerHTML = '';
                    
                    doctors.forEach(doctor => {
                        const row = document.createElement('tr');
                        const createdAt = new Date(doctor.created_at).toLocaleString();
                        
                        row.innerHTML = `
                            <td>${doctor.doctor_name}</td>
                            <td>${createdAt}</td>
                            <td>
                                <button class="delete-btn" onclick="deleteDoctor(${doctor.id}, this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        `;
                        
                        tbody.appendChild(row);
                    });
                })
                .catch(error => console.error('获取医生列表失败:', error));
        }

        // 删除医生记录
        function deleteDoctor(doctorId, button) {
            if (confirm('确定要删除这条记录吗？')) {
                fetch(`/delete_doctor/${doctorId}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        button.closest('tr').remove();
                    } else {
                        alert('删除失败：' + (result.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('删除医生记录失败:', error);
                    alert('删除失败，请稍后重试');
                });
            }
        }

        // 页面加载时加载医生列表
        document.addEventListener('DOMContentLoaded', loadDoctors);
    </script>
</body>
</html> 