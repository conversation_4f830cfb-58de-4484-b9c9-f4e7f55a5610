#!/bin/bash
# 服务器初始化和部署脚本
# 服务器: *************
# 域名: https://pm.beimoyinhenlinlin.cn/

set -e

echo "🚀 孕产妇管理系统 - 服务器部署脚本"
echo "=================================="
echo "服务器: *************"
echo "域名: https://pm.beimoyinhenlinlin.cn/"
echo "=================================="

# 1. 更新系统
echo "📦 更新系统包..."
sudo apt update && sudo apt upgrade -y

# 2. 安装必要软件
echo "📦 安装必要软件..."
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    nginx \
    mysql-server \
    git \
    curl \
    wget \
    ufw \
    certbot \
    python3-certbot-nginx

# 3. 配置MySQL
echo "🗄️ 配置MySQL..."
sudo systemctl start mysql
sudo systemctl enable mysql

# 设置MySQL root密码
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'windows1';"
sudo mysql -e "FLUSH PRIVILEGES;"

# 创建数据库
sudo mysql -u root -pwindows1 -e "CREATE DATABASE IF NOT EXISTS pregnancy_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 4. 创建应用目录
echo "📁 创建应用目录..."
sudo mkdir -p /var/www/pregnancy_management
sudo chown -R $USER:$USER /var/www/pregnancy_management

# 5. 创建日志目录
echo "📝 创建日志目录..."
sudo mkdir -p /var/log/pregnancy_management
sudo mkdir -p /var/log/nginx
sudo chown -R www-data:www-data /var/log/pregnancy_management

# 6. 配置防火墙
echo "🔒 配置防火墙..."
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw --force enable

# 7. 创建systemd服务文件
echo "⚙️ 创建systemd服务..."
sudo tee /etc/systemd/system/pregnancy-management.service > /dev/null << 'EOF'
[Unit]
Description=Pregnancy Management System
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/pregnancy_management
Environment="PATH=/var/www/pregnancy_management/venv/bin"
Environment="FLASK_CONFIG=production"
ExecStart=/var/www/pregnancy_management/venv/bin/gunicorn -c gunicorn_config.py app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 8. 创建Nginx配置
echo "🌐 创建Nginx配置..."
sudo tee /etc/nginx/sites-available/pregnancy-management > /dev/null << 'EOF'
server {
    listen 80;
    server_name pm.beimoyinhenlinlin.cn;
    
    # 临时配置，SSL配置后会重定向到HTTPS
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /var/www/pregnancy_management/static;
        expires 30d;
    }
    
    location /uploads {
        alias /var/www/pregnancy_management/uploads;
        expires 1d;
    }
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/pregnancy-management /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
sudo nginx -t

# 9. 重启服务
echo "🔄 启动服务..."
sudo systemctl daemon-reload
sudo systemctl restart nginx
sudo systemctl enable nginx

echo "✅ 服务器初始化完成！"
echo ""
echo "📋 下一步操作："
echo "1. 上传应用代码到 /var/www/pregnancy_management/"
echo "2. 在应用目录中运行: python3 -m venv venv"
echo "3. 激活虚拟环境: source venv/bin/activate"
echo "4. 安装依赖: pip install -r requirements.txt"
echo "5. 导入数据库表: mysql -u root -pwindows1 pregnancy_management < create_tables.sql"
echo "6. 启动应用服务: sudo systemctl start pregnancy-management"
echo "7. 配置SSL证书: sudo certbot --nginx -d pm.beimoyinhenlinlin.cn"
echo ""
echo "🌐 临时访问地址: http://pm.beimoyinhenlinlin.cn/"
echo "🔒 SSL配置后: https://pm.beimoyinhenlinlin.cn/"
