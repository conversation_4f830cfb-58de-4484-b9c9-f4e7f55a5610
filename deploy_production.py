#!/usr/bin/env python3
"""
生产环境部署脚本
服务器: *************
域名: https://pm.beimoyinhenlinlin.cn/
"""
import os
import sys
import subprocess

def check_environment():
    """检查部署环境"""
    print("🔍 检查部署环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python版本需要3.8或更高")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['flask', 'mysql.connector', 'pandas', 'openpyxl', 'gunicorn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '.'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 缺失")
    
    if missing_packages:
        print(f"\n请安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("\n🔗 测试数据库连接...")
    
    try:
        from config import get_db_connection
        conn = get_db_connection()
        if conn:
            conn.close()
            print("✅ 数据库连接成功")
            return True
        else:
            print("❌ 数据库连接失败")
            return False
    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")
        return False

def create_systemd_service():
    """创建systemd服务文件"""
    current_dir = os.getcwd()
    service_content = f"""[Unit]
Description=Pregnancy Management System
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory={current_dir}
Environment="PATH={current_dir}/venv/bin"
Environment="FLASK_CONFIG=production"
ExecStart={current_dir}/venv/bin/gunicorn -c gunicorn_config.py app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
"""
    
    service_file = "/etc/systemd/system/pregnancy-management.service"
    print(f"\n📝 创建systemd服务文件: {service_file}")
    print("请以root权限运行以下命令:")
    print(f"sudo tee {service_file} << 'EOF'")
    print(service_content)
    print("EOF")
    print("sudo systemctl daemon-reload")
    print("sudo systemctl enable pregnancy-management")
    print("sudo systemctl start pregnancy-management")

def create_gunicorn_config():
    """创建Gunicorn配置文件"""
    config_content = '''# Gunicorn配置文件
import multiprocessing

# 服务器配置
bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 进程管理
preload_app = True
daemon = False
pidfile = "/tmp/pregnancy_management.pid"

# 日志配置
accesslog = "/var/log/pregnancy_management/access.log"
errorlog = "/var/log/pregnancy_management/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# 超时配置
timeout = 30
keepalive = 2

# SSL配置（如果需要）
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
'''
    
    with open('gunicorn_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 创建Gunicorn配置文件: gunicorn_config.py")

def create_nginx_config():
    """创建Nginx配置"""
    nginx_config = '''server {
    listen 80;
    server_name pm.beimoyinhenlinlin.cn;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name pm.beimoyinhenlinlin.cn;
    
    # SSL配置
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/pregnancy_management_access.log;
    error_log /var/log/nginx/pregnancy_management_error.log;
    
    # 客户端上传限制
    client_max_body_size 20M;
    
    # 静态文件
    location /static {
        alias /path/to/pregnancy_management/static;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads {
        alias /path/to/pregnancy_management/uploads;
        expires 1d;
    }
    
    # 应用代理
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
}
'''
    
    with open('nginx_pregnancy_management.conf', 'w', encoding='utf-8') as f:
        f.write(nginx_config)
    
    print("✅ 创建Nginx配置文件: nginx_pregnancy_management.conf")
    print("请将此文件复制到 /etc/nginx/sites-available/ 并创建软链接到 sites-enabled/")

def create_deployment_script():
    """创建部署脚本"""
    deploy_script = '''#!/bin/bash
# 孕产妇管理系统部署脚本

set -e

echo "🚀 开始部署孕产妇管理系统..."

# 创建日志目录
sudo mkdir -p /var/log/pregnancy_management
sudo chown www-data:www-data /var/log/pregnancy_management

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo "📦 安装Python依赖..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# 设置文件权限
echo "🔒 设置文件权限..."
sudo chown -R www-data:www-data .
sudo chmod -R 755 .
sudo chmod 700 uploads/

# 测试应用
echo "🧪 测试应用..."
python test_app.py

# 重启服务
echo "🔄 重启服务..."
sudo systemctl restart pregnancy-management
sudo systemctl restart nginx

echo "✅ 部署完成！"
echo "🌐 访问地址: https://pm.beimoyinhenlinlin.cn/"
'''
    
    with open('deploy.sh', 'w', encoding='utf-8') as f:
        f.write(deploy_script)
    
    os.chmod('deploy.sh', 0o755)
    print("✅ 创建部署脚本: deploy.sh")

def main():
    """主函数"""
    print("=" * 60)
    print("🏥 孕产妇管理系统 - 生产环境部署")
    print("=" * 60)
    print(f"🌐 域名: https://pm.beimoyinhenlinlin.cn/")
    print(f"🖥️  服务器: *************")
    print(f"🗄️  数据库: MySQL (root/windows1)")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return 1
    
    # 测试数据库连接
    if not test_database_connection():
        print("\n❌ 数据库连接失败，请检查数据库配置")
        return 1
    
    # 创建配置文件
    print("\n📝 创建配置文件...")
    create_gunicorn_config()
    create_nginx_config()
    create_deployment_script()
    create_systemd_service()
    
    print("\n" + "=" * 60)
    print("🎉 部署配置创建完成！")
    print("=" * 60)
    print("\n📋 下一步操作:")
    print("1. 配置SSL证书")
    print("2. 运行 sudo ./deploy.sh 进行部署")
    print("3. 配置防火墙开放80和443端口")
    print("4. 配置域名DNS解析到服务器IP")
    print("\n🔧 手动操作:")
    print("- 复制nginx配置到 /etc/nginx/sites-available/")
    print("- 创建systemd服务文件")
    print("- 配置SSL证书")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
