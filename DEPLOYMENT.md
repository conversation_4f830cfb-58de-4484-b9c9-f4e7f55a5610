# 部署指南

## 开发环境部署

### 1. 环境准备
```bash
# 确保Python 3.8+已安装
python --version

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 数据库配置
1. 确保MySQL服务运行
2. 修改 `config.py` 中的数据库连接信息
3. 运行数据库初始化脚本：
```sql
mysql -u root -p < create_tables.sql
```

### 4. 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，设置正确的配置值
```

### 5. 启动应用
```bash
# 使用开发服务器
python run.py

# 或直接运行
python app.py
```

## 生产环境部署

### 1. 使用Gunicorn部署
```bash
# 安装gunicorn（已在requirements.txt中）
pip install gunicorn

# 启动应用
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

### 2. 使用systemd服务（Linux）
创建服务文件 `/etc/systemd/system/pregnancy-management.service`：
```ini
[Unit]
Description=Pregnancy Management System
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/pregnancy_management
Environment="PATH=/path/to/pregnancy_management/venv/bin"
ExecStart=/path/to/pregnancy_management/venv/bin/gunicorn -w 4 -b 127.0.0.1:8000 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable pregnancy-management
sudo systemctl start pregnancy-management
```

### 3. Nginx反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/pregnancy_management/static;
        expires 30d;
    }

    location /uploads {
        alias /path/to/pregnancy_management/uploads;
        expires 1d;
    }
}
```

## Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

### 2. 构建和运行
```bash
# 构建镜像
docker build -t pregnancy-management .

# 运行容器
docker run -d -p 5000:5000 \
  -e DB_HOST=your_db_host \
  -e DB_PASSWORD=your_db_password \
  pregnancy-management
```

## 安全配置

### 1. 环境变量
生产环境必须设置以下环境变量：
- `SECRET_KEY`: Flask密钥
- `DB_PASSWORD`: 数据库密码
- `FLASK_CONFIG=production`

### 2. 数据库安全
- 使用强密码
- 限制数据库访问IP
- 定期备份数据

### 3. 文件权限
```bash
# 设置适当的文件权限
chmod 755 /path/to/pregnancy_management
chmod 644 /path/to/pregnancy_management/*.py
chmod 700 /path/to/pregnancy_management/uploads
```

## 监控和维护

### 1. 日志监控
```bash
# 查看应用日志
tail -f app.log

# 查看系统服务日志
sudo journalctl -u pregnancy-management -f
```

### 2. 数据库备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p pregnancy_management > backup_$DATE.sql
```

### 3. 性能监控
- 监控CPU和内存使用
- 监控数据库连接数
- 监控响应时间

## 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

2. **文件上传失败**
   - 检查uploads目录权限
   - 验证文件大小限制
   - 检查磁盘空间

3. **登录问题**
   - 检查session配置
   - 验证SECRET_KEY设置
   - 清除浏览器缓存

### 调试模式
```bash
# 启用调试模式
export FLASK_DEBUG=true
python app.py
```
