<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日记录查询 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #ff6d00;
            --success-color: #43a047;
            --danger-color: #e53935;
            --background-color: #f5f7fa;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --hover-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            --border-radius: 8px;
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        .main-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .nav {
            background: white;
            padding: 1rem;
            box-shadow: var(--card-shadow);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        h1 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 日期选择器样式 */
        .date-selector {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .date-selector label {
            color: #4b5563;
            font-weight: 500;
        }

        .date-selector input {
            padding: 0.5rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            font-size: 0.95rem;
        }

        .date-selector button {
            padding: 0.5rem 1rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .date-selector button:hover {
            background: #1976d2;
        }

        /* 统计卡片样式 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-top: 4px solid var(--primary-color);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }

        .stats-card i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .stats-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0.5rem 0;
        }

        .stats-card .label {
            color: #7f8c8d;
            font-size: 1rem;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }

        .table-container h2 {
            color: #2c3e50;
            font-size: 1.4rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background: #f8f9fa;
            color: #2c3e50;
            font-weight: 600;
            padding: 1rem;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            color: #2c3e50;
        }

        .table tr:hover td {
            background: #f8f9fa;
        }

        /* 状态标签样式 */
        .status-tag {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .status-tag.high-risk {
            background: #fee2e2;
            color: #dc2626;
        }

        .status-tag.vip {
            background: #fff7ed;
            color: #ea580c;
        }

        /* 空数据提示样式 */
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button">
            <i class="fas fa-home"></i> 返回首页
        </a>
    </div>

    <div class="main-container">
        <h1><i class="fas fa-calendar-day"></i> 每日记录查询</h1>

        <!-- 日期选择器 -->
        <div class="date-selector">
            <label for="date">选择日期：</label>
            <input type="date" id="date" value="{{ query_date }}" onchange="changeDate(this.value)">
            <button onclick="setToday()">
                <i class="fas fa-calendar-day"></i> 今天
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stats-card">
                <i class="fas fa-users"></i>
                <div class="number">{{ total_patients }}</div>
                <div class="label">新增孕产妇</div>
            </div>
            <div class="stats-card">
                <i class="fas fa-notes-medical"></i>
                <div class="number">{{ total_checkups }}</div>
                <div class="label">产检记录</div>
            </div>
            <div class="stats-card">
                <i class="fas fa-exclamation-triangle"></i>
                <div class="number">{{ high_risk_count }}</div>
                <div class="label">高危孕产妇</div>
            </div>
            <div class="stats-card">
                <i class="fas fa-baby"></i>
                <div class="number">{{ birth_count }}</div>
                <div class="label">分娩数量</div>
            </div>
        </div>

        <!-- 孕产妇信息表格 -->
        <div class="table-container">
            <h2><i class="fas fa-user-plus"></i> 新增孕产妇信息</h2>
            {% if patients %}
            <table class="table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>录入时间</th>
                        <th>姓名</th>
                        <th>年龄</th>
                        <th>联系电话</th>
                        <th>主管医生</th>
                        <th>状态标记</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for patient in patients %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ patient.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>{{ patient.name }}</td>
                        <td>{{ patient.age }}</td>
                        <td>{{ patient.phone }}</td>
                        <td>{{ patient.doctor_in_charge }}</td>
                        <td>
                            {% if patient.is_high_risk == '高危' %}
                            <span class="status-tag high-risk">
                                <i class="fas fa-exclamation-triangle"></i> 高危
                            </span>
                            {% endif %}
                            {% if patient.is_vip %}
                            <span class="status-tag vip">
                                <i class="fas fa-crown"></i> VIP
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('patient_records', patient_id=patient.id) }}" class="button small">
                                <i class="fas fa-eye"></i> 查看
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="no-data">该日期没有新增孕产妇信息。</p>
            {% endif %}
        </div>

        <!-- 产检记录表格 -->
        <div class="table-container">
            <h2><i class="fas fa-stethoscope"></i> 产检记录</h2>
            {% if checkups %}
            <table class="table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>录入时间</th>
                        <th>病人姓名</th>
                        <th>产检日期</th>
                        <th>医生</th>
                        <th>风险因素</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for checkup in checkups %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ checkup.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>{{ checkup.name }}</td>
                        <td>{{ checkup.checkup_date }}</td>
                        <td>{{ checkup.doctor }}</td>
                        <td>{{ checkup.risk_factors or '无' }}</td>
                        <td>
                            <a href="{{ url_for('checkup_detail', record_id=checkup.record_id) }}" class="button small">
                                <i class="fas fa-eye"></i> 查看
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="no-data">该日期没有产检记录。</p>
            {% endif %}
        </div>
    </div>

    <script>
        function changeDate(date) {
            window.location.href = `{{ url_for('todays_entries') }}?date=${date}`;
        }

        function setToday() {
            const today = new Date().toISOString().split('T')[0];
            changeDate(today);
        }

        // 页面加载时设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('date');
            if (!dateInput.value) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.value = today;
                changeDate(today);
            }
        });
    </script>
</body>
</html> 