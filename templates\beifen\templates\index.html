<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .logout-button {
            display: inline-block;
            padding: 10px 15px;
            font-size: 16px;
            color: white;
            background-color: #007BFF;
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .logout-button:hover {
            background-color: #0056b3;
        }

        .logout-container {
            display: flex;
            justify-content: center; /* 水平居中 */
            margin-top: 20px; /* 可选：调整顶部间距 */
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>涟源市妇幼保健院孕妇产检信息管理系统</h1>
        <p class="subtitle" style="text-align: center;">专业的孕期健康管理平台</p>
    </div>

    <div class="dashboard">
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h3>孕产妇管理</h3>
                <div class="feature-actions">
                    <a href="{{ url_for('add_patient') }}" class="button">
                        <i class="fas fa-plus"></i> 添加新孕产妇
                    </a>
                    <a href="{{ url_for('search_patient') }}" class="button">
                        <i class="fas fa-search"></i> 查询孕产妇
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <h3>预约管理</h3>
                <div class="feature-actions">
                    <a href="{{ url_for('appointments') }}" class="button">
                        <i class="fas fa-clock"></i> 预约查询
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-file-import"></i>
                </div>
                <h3>数据管理</h3>
                <div class="feature-actions">
                    <a href="{{ url_for('import_patients') }}" class="button">
                        <i class="fas fa-upload"></i> 批量导入
                    </a>
                    <a href="{{ url_for('export_records') }}" class="button">
                        <i class="fas fa-download"></i> 导出记录
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <h3>当天录入情况</h3>
                <div class="feature-actions">
                    <a href="{{ url_for('todays_entries') }}" class="button">
                        <i class="fas fa-eye"></i> 查看当天录入情况
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <div class="messages-container">
                {% for message in messages %}
                    <div class="alert">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    <h1>欢迎，{{ username }}！</h1>
    <div class="logout-container">
        <a href="{{ url_for('logout') }}" class="logout-button">登出</a>
    </div>
    <footer class="footer">
        <p>© 2024 孕妇产检信息管理系统 | 软件制作：蒋琳琳</p>
    </footer>
</body>
</html> 