import pandas as pd

# 创建示例数据
data = {
    '姓名': ['示例姓名'],
    '年龄': [25],
    '身份证号': ['430000000000000000'],
    '门诊号': ['00000001'], 
    '地址': ['示例地址'],
    '联系电话': ['13800000000'],
    '丈夫姓名': ['示例丈夫姓名'],
    '预产期': ['2024-12-31'],
    '末次月经': ['2024-04-01'],
    '首次产检日期': ['2024-05-01'],
    '登记日期': ['2024-05-01'],
    '主管医生': ['示例医生'],
    '管理机构': ['涟源市妇幼保健院'],
    '怀孕次数': [1],
    '分娩次数': [0],
    '是否高危': ['否'],  # 值为"高危"或"否"
    '是否VIP': ['否']
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为Excel模板
df.to_excel('static/template.xlsx', index=False) 