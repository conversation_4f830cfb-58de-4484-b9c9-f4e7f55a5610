USE pregnancy_management;

-- 修改patient_info表
ALTER TABLE patient_info
    MODIFY COLUMN is_high_risk VARCHAR(10) DEFAULT '否',  -- 改为VARCHAR以存储"高危"或"否"
    MODIFY COLUMN is_vip BOOLEAN DEFAULT FALSE,
    MODIFY COLUMN name VARCHAR(50) NOT NULL,
    MODIFY COLUMN age INT NOT NULL,
    MODIFY COLUMN id_card VARCHAR(18) NOT NULL,
    MODIFY COLUMN outpatient_number VARCHAR(50),  -- 改为可空
    MODIFY COLUMN phone VARCHAR(20) NOT NULL,
    MODIFY COLUMN address TEXT NOT NULL,
    MODIFY COLUMN husband_name VARCHAR(50),
    MODIFY COLUMN due_date DATE NOT NULL,
    MODIFY COLUMN last_menstrual_date DATE NOT NULL,
    MODIFY COLUMN first_visit_date DATE NOT NULL,
    MODIFY COLUMN registration_date DATE NOT NULL,
    MODIFY COLUMN doctor_in_charge VARCHAR(50) NOT NULL,
    MODIFY COLUMN management_org VARCHAR(100) NOT NULL DEFAULT '涟源市妇幼保健院',
    MODIFY COLUMN pregnancy_count INT NOT NULL DEFAULT 1,
    MODIFY COLUMN delivery_count INT NOT NULL DEFAULT 0;

-- 更新现有记录的is_high_risk值
UPDATE patient_info SET is_high_risk = '否' WHERE is_high_risk IS NULL OR is_high_risk = '';
UPDATE patient_info SET is_high_risk = '高危' WHERE is_high_risk = '是' OR is_high_risk = 'true' OR is_high_risk = '1';

-- 添加索引以提高查询性能
ALTER TABLE patient_info
    ADD INDEX idx_phone (phone),
    ADD INDEX idx_id_card (id_card),
    ADD INDEX idx_outpatient_number (outpatient_number);

-- 修改checkup_records表
ALTER TABLE checkup_records
    MODIFY COLUMN patient_id INT NOT NULL,
    MODIFY COLUMN doctor VARCHAR(50) NOT NULL,
    MODIFY COLUMN risk_color VARCHAR(20),
    MODIFY COLUMN checkup_date DATE NOT NULL,
    MODIFY COLUMN appointment_date DATE,
    MODIFY COLUMN fundal_height FLOAT,
    MODIFY COLUMN abdominal_circumference FLOAT,
    MODIFY COLUMN blood_pressure VARCHAR(20),
    MODIFY COLUMN weight FLOAT,
    MODIFY COLUMN fetal_heart_rate INT,
    MODIFY COLUMN notes TEXT,
    ADD COLUMN notification_status BOOLEAN DEFAULT FALSE;

-- 添加索引以提高查询性能
ALTER TABLE checkup_records
    ADD INDEX idx_checkup_date (checkup_date),
    ADD INDEX idx_appointment_date (appointment_date);

-- 更新doctor_history表
ALTER TABLE doctor_history
    MODIFY COLUMN doctor_name VARCHAR(50) NOT NULL,
    ADD UNIQUE INDEX idx_doctor_name (doctor_name); 