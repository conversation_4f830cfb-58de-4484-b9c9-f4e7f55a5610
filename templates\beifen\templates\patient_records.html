<!DOCTYPE html>
<html>
<head>
    <title>孕产妇记录 - 孕妇产检信息管理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav">
        <a href="{{ url_for('index') }}" class="button secondary">
            <i class="fas fa-home"></i> 返回首页
        </a>
        <a href="{{ url_for('search_patient') }}" class="button secondary">
            <i class="fas fa-search"></i> 返回搜索
        </a>
    </div>

    <div class="main-container">
        <!-- 页面标题和操作按钮 -->
        <div class="page-header">
            <div class="header-content">
                <h1>
                    <i class="fas fa-user"></i> 
                    {{ patient.name }} 的孕产妇记录
                    {% if patient.is_high_risk == 'true' %}
                    <span class="status-tag risk-red">
                        <i class="fas fa-exclamation-triangle"></i> 高危
                    </span>
                    {% endif %}
                    {% if patient.is_vip == 'true' %}
                    <span class="status-tag vip">
                        <i class="fas fa-crown"></i> VIP
                    </span>
                    {% endif %}
                </h1>
                <div class="patient-actions">
                    <a href="{{ url_for('add_checkup', patient_id=patient.id) }}" class="button">
                        <i class="fas fa-plus"></i> 添加产检记录
                    </a>
                    <a href="{{ url_for('edit_patient', patient_id=patient.id) }}" class="button secondary">
                        <i class="fas fa-edit"></i> 编辑信息
                    </a>
                    <button onclick="confirmDelete()" class="button delete">
                        <i class="fas fa-trash"></i> 删除孕产妇
                    </button>
                </div>
            </div>
        </div>

        <form id="deleteForm" action="{{ url_for('delete_patient', patient_id=patient.id) }}" method="post" style="display: none;"></form>

        {% if patient %}
        <!-- 孕产妇基本信息 -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">基本信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tbody>
                        <tr>
                            <th width="150">姓名：</th>
                            <td>{{ patient.name }}</td>
                        </tr>
                        <tr>
                            <th width="150">年龄：</th>
                            <td>{{ patient.age }}</td>
                        </tr>
                        <tr>
                            <th width="150">门诊病历号：</th>
                            <td>{{ patient.outpatient_number }}</td>
                        </tr>
                        <tr>
                            <th width="150">身份证号：</th>
                            <td>{{ patient.id_card }}</td>
                        </tr>
                        <tr>
                            <th width="150">联系电话：</th>
                            <td>{{ patient.phone }}</td>
                        </tr>
                        <tr>
                            <th width="150">现居住地：</th>
                            <td>{{ patient.address }}</td>
                        </tr>
                        <tr>
                            <th width="150">丈夫姓名：</th>
                            <td>{{ patient.husband_name }}</td>
                        </tr>
                        <tr>
                            <th width="150">是否高危：</th>
                            <td>{{ patient.is_high_risk}}</td>
                        </tr>
                        <tr>
                            <th width="150">高危因素：</th>
                            <td>{{ patient.risk_factors if patient.risk_factors else '无' }}</td>
                        </tr>
                        <tr>
                            <th width="150">分娩状态：</th>
                            <td>{{ patient.has_given_birth if patient.has_given_birth else '在孕' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 就医信息 -->
        <div class="card">
            <div class="detail-section">
                <h2><i class="fas fa-calendar-alt"></i> 就医信息</h2>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>末次月经</label>
                        <span>{{ patient.last_menstrual_date }}</span>
                    </div>
                    <div class="detail-item">
                        <label>怀孕次数</label>
                        <span class="editable" data-field="pregnancy_count" data-id="{{ patient.id }}">{{ patient.pregnancy_count }}</span>
                    </div>
                    <div class="detail-item">
                        <label>分娩次数</label>
                        <span class="editable" data-field="delivery_count" data-id="{{ patient.id }}">{{ patient.delivery_count }}</span>
                    </div>
                    <div class="detail-item">
                        <label>当前孕周</label>
                        <span class="pregnancy-week">
                            {% if patient.has_given_birth == '已分娩' %}
                                已分娩
                            {% elif patient.has_given_birth == '已流产' %}
                                已流产
                            {% elif patient.has_given_birth == '已引产' %}
                                已引产
                            {% else %}
                                {{ patient.pregnancy_week }}
                            {% endif %}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>预产期</label>
                        <span>{{ patient.due_date }}</span>
                    </div>
                    <div class="detail-item">
                        <label>首次产检日期</label>
                        <span>{{ patient.first_visit_date }}</span>
                    </div>
                    <div class="detail-item">
                        <label>登记日期</label>
                        <span>{{ patient.registration_date }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="detail-section">
            <h2><i class="fas fa-user-md"></i> 管理信息</h2>
            <div class="detail-grid">
                <div class="detail-item">
                    <label>主管医生</label>
                    <span>{{ patient.doctor_in_charge }}</span>
                </div>
                <div class="detail-item">
                    <label>管理机构</label>
                    <span>{{ patient.management_org }}</span>
                </div>
                <div class="detail-item">
                    <label>是否VIP</label>
                    <span class="editable" data-field="is_vip" data-type="select" data-options="是,否" data-id="{{ patient.id }}">{{ '是' if patient.is_vip else '否' }}</span>
                </div>
                <div class="detail-item">
                    <label>分娩状态</label>
                    <span>{{ patient.has_given_birth if patient.has_given_birth else '在孕' }}</span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 编辑选项 -->
        <!-- <select class="edit-select" style="display: none;">
            <option value="高危">高危</option>
            <option value="否">否</option>
        </select> -->
        <!-- <select name="is_high_risk" class="form-control">
            <option value="true" {% if patient.is_high_risk == '高危' %}selected{% endif %}>是</option>
            <option value="false" {% if patient.is_high_risk == '否' %}selected{% endif %}>否</option>
        </select> -->
        
        <!-- 产检记录 -->
        {% if records %}
        <div class="card">
            <div class="detail-section">
                <h2>
                    <i class="fas fa-notes-medical"></i> 产检记录
                    <span class="record-count">共 {{ records|length }} 条记录</span>
                </h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar"></i> 产检日期</th>
                                <th><i class="fas fa-user-md"></i> 医生</th>
                                <th><i class="fas fa-exclamation-triangle"></i> 高危因素</th>
                                <th><i class="fas fa-tag"></i> 风险等级</th>
                                <th><i class="fas fa-ruler-vertical"></i> 宫高</th>
                                <th><i class="fas fa-ruler"></i> 腹围</th>
                                <th><i class="fas fa-heart"></i> 血压</th>
                                <th><i class="fas fa-weight"></i> 体重</th>
                                <th><i class="fas fa-heartbeat"></i> 胎心</th>
                                <th><i class="fas fa-cog"></i> 操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in records %}
                            <tr class="record-row">
                                <td>{{ record.checkup_date }}</td>
                                <td>{{ record.doctor }}</td>
                                <td>{{ record.risk_factors }}</td>
                                <td>
                                    {% if record.risk_color %}
                                    <span class="status-tag risk-{{ record.risk_color }}">
                                        {{ record.risk_color }}
                                    </span>
                                    {% else %}
                                    <span class="status-tag">无风险</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.fundal_height }}</td>
                                <td>{{ record.abdominal_circumference }}</td>
                                <td>{{ record.blood_pressure }}</td>
                                <td>{{ record.weight }}</td>
                                <td>{{ record.fetal_heart_rate }}</td>
                                <td>
                                    <a href="{{ url_for('checkup_detail', record_id=record.record_id) }}" class="action-button view">
                                        <i class="fas fa-eye"></i> 详情
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card">
            <div class="no-records">
                <i class="fas fa-clipboard-list"></i>
                <h3>暂无产检记录</h3>
                <p>点击"添加产检记录"按钮开始记录产检信息</p>
                <div class="patient-actions">
                    <a href="{{ url_for('add_checkup', patient_id=patient.id) }}" class="button">
                        <i class="fas fa-plus"></i> 添加产检记录
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                <div class="alert {{ category }}">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ message }}
                </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <style>
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: calc(var(--spacing-unit) * 2);
        }
        
        .header-content h1 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 2);
            flex-wrap: wrap;
        }
        
        .patient-actions {
            display: flex;
            gap: var(--spacing-unit);
            flex-wrap: wrap;
        }
        
        .detail-section {
            padding: calc(var(--spacing-unit) * 3) 0;
            border-bottom: 2px solid #1e88e5;
        }
        
        .detail-section:first-child {
            padding-top: 0;
        }
        
        .detail-section:last-child {
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .detail-section h2 {
            color: var(--text-primary);
            font-size: 1.2rem;
            margin: 0 0 calc(var(--spacing-unit) * 2);
            display: flex;
            align-items: center;
            gap: var(--spacing-unit);
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: calc(var(--spacing-unit) * 3);
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-unit);
        }
        
        .detail-item label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .detail-item span {
            color: var(--text-primary);
            font-size: 1rem;
        }
        
        .edit-hint {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-left: auto;
        }
        
        .editable {
            padding: calc(var(--spacing-unit) * 0.5);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .editable:hover {
            background: var(--background-primary);
        }
        
        .record-count {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-left: auto;
        }
        
        .no-records {
            text-align: center;
            padding: calc(var(--spacing-unit) * 4);
        }
        
        .no-records i {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: calc(var(--spacing-unit) * 2);
        }
        
        .no-records h3 {
            margin: calc(var(--spacing-unit) * 2) 0;
            color: var(--text-primary);
        }
        
        .no-records p {
            color: var(--text-secondary);
            margin-bottom: calc(var(--spacing-unit) * 3);
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .patient-actions {
                width: 100%;
            }
            
            .patient-actions .button {
                flex: 1;
                text-align: center;
            }
            
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .edit-hint {
                display: none;
            }
        }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const editableCells = document.querySelectorAll('.editable');
        
        editableCells.forEach(cell => {
            cell.addEventListener('dblclick', function() {
                const originalContent = cell.textContent.trim();
                const field = cell.dataset.field;
                const patientId = cell.dataset.id;
                const fieldType = cell.dataset.type || 'text';
                
                // 创建输入框
                let input;
                if (field === 'is_high_risk' || field === 'is_vip') {
                    input = document.createElement('select');
                    input.innerHTML = `
                        <option value="false">否</option>
                        <option value="true">是</option>
                    `;
                    input.value = originalContent === '是' ? 'true' : 'false';
                } else if (fieldType === 'date') {
                    input = document.createElement('input');
                    input.type = 'date';
                    input.value = originalContent;
                } else if (fieldType === 'number') {
                    input = document.createElement('input');
                    input.type = 'number';
                    input.value = originalContent;
                } else {
                    input = document.createElement('input');
                    input.type = 'text';
                    input.value = originalContent;
                }
                
                // 设置输入框样式
                input.style.width = '100%';
                input.style.padding = 'calc(var(--spacing-unit) * 0.5)';
                input.style.border = '1px solid var(--primary-color)';
                input.style.borderRadius = 'var(--border-radius)';
                input.style.fontSize = '1rem';
                
                cell.textContent = '';
                cell.appendChild(input);
                input.focus();
                
                function saveChanges() {
                    const newValue = input.value;
                    
                    // 创建表单数据
                    const formData = new FormData();
                    formData.append('field', field);
                    formData.append('value', newValue);
                    formData.append('inline_edit', 'true');
                    
                    // 发送更新请求
                    fetch(`/update_patient/${patientId}`, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (field === 'is_high_risk' || field === 'is_vip') {
                                cell.textContent = newValue === 'true' ? '是' : '否';
                            } else {
                                cell.textContent = newValue;
                            }
                        } else {
                            cell.textContent = originalContent;
                            const alert = document.createElement('div');
                            alert.className = 'alert error';
                            alert.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${data.message}`;
                            document.querySelector('.main-container').insertBefore(alert, document.querySelector('.main-container').firstChild);
                            setTimeout(() => alert.remove(), 3000);
                        }
                    })
                    .catch(error => {
                        cell.textContent = originalContent;
                        const alert = document.createElement('div');
                        alert.className = 'alert error';
                        alert.innerHTML = `<i class="fas fa-exclamation-circle"></i> 更新失败：${error}`;
                        document.querySelector('.main-container').insertBefore(alert, document.querySelector('.main-container').firstChild);
                        setTimeout(() => alert.remove(), 3000);
                    });
                }
                
                // 处理失去焦点和回车事件
                input.addEventListener('blur', saveChanges);
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        saveChanges();
                    }
                });
            });
        });
    });

    function confirmDelete() {
        if (confirm('确定要删除该孕产妇的所有信息吗？此操作不可恢复！')) {
            document.getElementById('deleteForm').submit();
        }
    }

    function calculateDueDate(lastMenstrualDate) {
        // 将日期字符串转换为 Date 对象
        let date = new Date(lastMenstrualDate);
        
        let year = date.getFullYear();
        let month = date.getMonth() + 1; // JavaScript 月份从0开始
        let day = date.getDate();
        
        // 根据月份计算年份和新月份
        if (month > 3) {
            month = month - 3;
            year = year + 1;
        } else {
            month = month + 9;
        }
        
        // 处理日期加7的情况
        day = day + 7;
        
        // 获取目标月份的最大天数
        let lastDayOfMonth = new Date(year, month, 0).getDate();
        
        // 如果日期超出当月最大天数，则顺延到下月
        if (day > lastDayOfMonth) {
            day = day - lastDayOfMonth;
            month++;
            if (month > 12) {
                month = 1;
                year++;
            }
        }
        
        // 格式化月份和日期，确保是两位数
        month = month.toString().padStart(2, '0');
        day = day.toString().padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    }

    // 在页面加载时添加自动计算预产期的功能
    document.addEventListener('DOMContentLoaded', function() {
        // 找到末次月经日期的输入框
        const lastMenstrualInput = document.querySelector('input[name="last_menstrual_date"]');
        const dueDateInput = document.querySelector('input[name="due_date"]');
        
        if (lastMenstrualInput && dueDateInput) {
            lastMenstrualInput.addEventListener('change', function() {
                if (this.value) {
                    dueDateInput.value = calculateDueDate(this.value);
                }
            });
        }
    });
    </script>
</body>
</html> 