#!/bin/bash
# 创建systemd服务和完整部署

set -e

echo "🔧 创建systemd服务和完整部署"
echo "============================="

# 1. 创建日志目录
echo "📝 创建日志目录..."
sudo mkdir -p /var/log/pregnancy_management
sudo chown -R www-data:www-data /var/log/pregnancy_management

# 2. 创建systemd服务文件
echo "⚙️ 创建systemd服务文件..."
sudo tee /etc/systemd/system/pregnancy-management.service > /dev/null << EOF
[Unit]
Description=Pregnancy Management System
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/pregnancy_management
Environment="PATH=/var/www/pregnancy_management/venv/bin"
Environment="FLASK_CONFIG=production"
ExecStart=/var/www/pregnancy_management/venv/bin/gunicorn -c gunicorn_config.py app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

echo "✅ systemd服务文件创建成功"

# 3. 重新加载systemd
echo "🔄 重新加载systemd..."
sudo systemctl daemon-reload

# 4. 设置文件权限
echo "🔒 设置文件权限..."
sudo chown -R www-data:www-data /var/www/pregnancy_management
sudo chmod -R 755 /var/www/pregnancy_management

# 5. 创建虚拟环境（如果不存在）
echo "🐍 设置Python虚拟环境..."
cd /var/www/pregnancy_management

if [ ! -d "venv" ]; then
    sudo -u www-data python3 -m venv venv
    echo "✅ 虚拟环境创建成功"
else
    echo "✅ 虚拟环境已存在"
fi

# 6. 安装依赖
echo "📦 安装Python依赖..."
sudo -u www-data venv/bin/pip install --upgrade pip
sudo -u www-data venv/bin/pip install -r requirements.txt

# 7. 创建必要目录
echo "📁 创建必要目录..."
sudo -u www-data mkdir -p uploads
sudo -u www-data mkdir -p logs

# 8. 创建Gunicorn配置文件
echo "⚙️ 创建Gunicorn配置..."
sudo -u www-data tee gunicorn_config.py > /dev/null << 'EOF'
import multiprocessing

# 服务器配置
bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 进程管理
preload_app = True
daemon = False
pidfile = "/tmp/pregnancy_management.pid"

# 日志配置
accesslog = "/var/log/pregnancy_management/access.log"
errorlog = "/var/log/pregnancy_management/error.log"
loglevel = "info"

# 超时配置
timeout = 30
keepalive = 2
EOF

# 9. 测试应用
echo "🧪 测试应用导入..."
sudo -u www-data venv/bin/python -c "
import sys
sys.path.insert(0, '/var/www/pregnancy_management')
try:
    import app
    print('✅ 应用导入成功')
except Exception as e:
    print(f'❌ 应用导入失败: {e}')
    import traceback
    traceback.print_exc()
    exit(1)
"

# 10. 启动服务
echo "🚀 启动pregnancy-management服务..."
sudo systemctl enable pregnancy-management
sudo systemctl start pregnancy-management

# 等待服务启动
sleep 5

# 11. 检查服务状态
echo "📊 检查服务状态..."
if sudo systemctl is-active --quiet pregnancy-management; then
    echo "✅ pregnancy-management服务运行正常"
    sudo systemctl status pregnancy-management --no-pager -l
else
    echo "❌ pregnancy-management服务启动失败"
    echo ""
    echo "=== 服务状态 ==="
    sudo systemctl status pregnancy-management --no-pager -l
    echo ""
    echo "=== 最近日志 ==="
    sudo journalctl -u pregnancy-management --no-pager -n 20
    echo ""
    echo "=== 错误日志 ==="
    if [ -f "/var/log/pregnancy_management/error.log" ]; then
        tail -n 20 /var/log/pregnancy_management/error.log
    else
        echo "错误日志文件不存在"
    fi
    exit 1
fi

# 12. 测试Web服务
echo "🧪 测试Web服务..."
sleep 2
if curl -s http://127.0.0.1:8000 > /dev/null; then
    echo "✅ Web服务响应正常"
else
    echo "❌ Web服务无响应"
    echo "检查端口监听状态:"
    sudo netstat -tlnp | grep :8000 || echo "端口8000未监听"
fi

# 13. 配置Nginx（如果存在）
if command -v nginx > /dev/null; then
    echo "🌐 配置Nginx..."
    
    # 创建Nginx配置
    sudo tee /etc/nginx/sites-available/pregnancy-management > /dev/null << 'EOF'
server {
    listen 80;
    server_name pm.beimoyinhenlinlin.cn;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /var/www/pregnancy_management/static;
        expires 30d;
    }
    
    location /uploads {
        alias /var/www/pregnancy_management/uploads;
        expires 1d;
    }
}
EOF

    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/pregnancy-management /etc/nginx/sites-enabled/
    
    # 测试Nginx配置
    if sudo nginx -t; then
        echo "✅ Nginx配置正确"
        sudo systemctl reload nginx
        echo "✅ Nginx重新加载成功"
    else
        echo "❌ Nginx配置错误"
    fi
else
    echo "⚠️ Nginx未安装，跳过Web服务器配置"
fi

echo ""
echo "🎉 服务部署完成！"
echo "=================="
echo "📍 内部地址: http://127.0.0.1:8000"
echo "🌐 外部地址: http://pm.beimoyinhenlinlin.cn/"
echo ""
echo "📋 管理命令："
echo "sudo systemctl status pregnancy-management    # 查看服务状态"
echo "sudo systemctl restart pregnancy-management   # 重启服务"
echo "sudo journalctl -u pregnancy-management -f    # 查看实时日志"
echo "curl http://127.0.0.1:8000                   # 测试内部访问"
