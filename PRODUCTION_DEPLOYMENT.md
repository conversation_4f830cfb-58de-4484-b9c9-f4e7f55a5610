# 生产环境部署指南

## 🎯 部署目标
- **服务器IP**: *************
- **域名**: https://pm.beimoyinhenlinlin.cn/
- **数据库**: MySQL (root/windows1)
- **Web服务器**: Nginx + Gunicorn

## 🚀 快速部署

### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y python3 python3-pip python3-venv nginx mysql-server git

# 安装Python依赖
pip3 install --upgrade pip
```

### 2. 上传代码
```bash
# 上传项目文件到服务器
scp -r pregnancy_management/ root@*************:/var/www/

# 或使用git克隆
cd /var/www/
git clone <your-repo-url> pregnancy_management
cd pregnancy_management
```

### 3. 运行部署脚本
```bash
# 运行部署配置脚本
python3 deploy_production.py

# 执行部署
chmod +x deploy.sh
sudo ./deploy.sh
```

## 📋 详细部署步骤

### 1. 数据库配置
```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库
CREATE DATABASE pregnancy_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户（如果需要）
CREATE USER 'pregnancy_user'@'%' IDENTIFIED BY 'windows1';
GRANT ALL PRIVILEGES ON pregnancy_management.* TO 'pregnancy_user'@'%';
FLUSH PRIVILEGES;

# 导入表结构
mysql -u root -p pregnancy_management < create_tables.sql
```

### 2. Python环境配置
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. Gunicorn配置
```bash
# 测试Gunicorn
gunicorn --bind 0.0.0.0:8000 app:app

# 使用配置文件启动
gunicorn -c gunicorn_config.py app:app
```

### 4. Systemd服务配置
```bash
# 创建服务文件
sudo nano /etc/systemd/system/pregnancy-management.service

# 内容如下：
[Unit]
Description=Pregnancy Management System
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/pregnancy_management
Environment="PATH=/var/www/pregnancy_management/venv/bin"
Environment="FLASK_CONFIG=production"
ExecStart=/var/www/pregnancy_management/venv/bin/gunicorn -c gunicorn_config.py app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target

# 启用服务
sudo systemctl daemon-reload
sudo systemctl enable pregnancy-management
sudo systemctl start pregnancy-management
sudo systemctl status pregnancy-management
```

### 5. Nginx配置
```bash
# 复制配置文件
sudo cp nginx_pregnancy_management.conf /etc/nginx/sites-available/pregnancy-management

# 创建软链接
sudo ln -s /etc/nginx/sites-available/pregnancy-management /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 6. SSL证书配置
```bash
# 使用Let's Encrypt（推荐）
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d pm.beimoyinhenlinlin.cn

# 或手动配置SSL证书
# 将证书文件放置到 /etc/ssl/certs/
# 将私钥文件放置到 /etc/ssl/private/
```

### 7. 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3306  # MySQL（如果需要外部访问）
sudo ufw enable
```

## 🔧 配置文件说明

### 数据库连接
- **主机**: *************
- **用户**: root
- **密码**: windows1
- **数据库**: pregnancy_management

### 应用配置
- **内部端口**: 8000
- **外部端口**: 80/443
- **工作进程**: 4个
- **超时时间**: 30秒

## 🧪 测试部署

### 1. 测试应用
```bash
# 运行测试脚本
python test_app.py

# 测试数据库连接
python -c "from config import get_db_connection; print('OK' if get_db_connection() else 'FAIL')"
```

### 2. 测试Web服务
```bash
# 测试内部服务
curl http://127.0.0.1:8000

# 测试外部访问
curl https://pm.beimoyinhenlinlin.cn/
```

## 📊 监控和维护

### 1. 查看日志
```bash
# 应用日志
sudo journalctl -u pregnancy-management -f

# Nginx日志
sudo tail -f /var/log/nginx/pregnancy_management_access.log
sudo tail -f /var/log/nginx/pregnancy_management_error.log

# 应用日志
tail -f /var/log/pregnancy_management/error.log
```

### 2. 服务管理
```bash
# 重启应用
sudo systemctl restart pregnancy-management

# 重启Nginx
sudo systemctl restart nginx

# 查看状态
sudo systemctl status pregnancy-management
sudo systemctl status nginx
```

### 3. 数据库备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p pregnancy_management > /backup/pregnancy_management_$DATE.sql

# 设置定时备份
crontab -e
# 添加：0 2 * * * /path/to/backup_script.sh
```

## 🚨 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证连接参数
   - 检查防火墙设置

2. **应用无法启动**
   - 检查Python依赖
   - 查看错误日志
   - 验证文件权限

3. **域名无法访问**
   - 检查DNS解析
   - 验证SSL证书
   - 检查Nginx配置

### 性能优化
1. **数据库优化**
   - 添加索引
   - 优化查询
   - 配置连接池

2. **应用优化**
   - 增加工作进程
   - 配置缓存
   - 优化静态文件

## 📞 技术支持

如遇问题，请检查：
1. 系统日志
2. 应用日志
3. 数据库连接
4. 网络配置

---
**部署完成后，系统将在 https://pm.beimoyinhenlinlin.cn/ 提供服务**
