#!/bin/bash
# 快速部署脚本 - 适用于已在目标目录的情况

set -e

echo "🚀 快速部署孕产妇管理系统"
echo "========================"
echo "当前目录: $(pwd)"

# 1. 设置文件权限
echo "🔒 设置文件权限..."
sudo chown -R www-data:www-data .
sudo chmod -R 755 .

# 2. 创建虚拟环境
echo "🐍 创建Python虚拟环境..."
if [ ! -d "venv" ]; then
    sudo -u www-data python3 -m venv venv
    echo "✅ 虚拟环境创建成功"
else
    echo "✅ 虚拟环境已存在"
fi

# 3. 安装依赖
echo "📦 安装Python依赖..."
sudo -u www-data venv/bin/pip install --upgrade pip
sudo -u www-data venv/bin/pip install -r requirements.txt

# 4. 创建必要目录
echo "📁 创建必要目录..."
sudo -u www-data mkdir -p uploads
sudo -u www-data mkdir -p logs
sudo chmod 755 uploads
sudo chmod 755 logs

# 5. 创建Gunicorn配置文件
echo "⚙️ 创建Gunicorn配置..."
sudo -u www-data tee gunicorn_config.py > /dev/null << 'EOF'
import multiprocessing

# 服务器配置
bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 进程管理
preload_app = True
daemon = False
pidfile = "/tmp/pregnancy_management.pid"

# 日志配置
accesslog = "/var/log/pregnancy_management/access.log"
errorlog = "/var/log/pregnancy_management/error.log"
loglevel = "info"

# 超时配置
timeout = 30
keepalive = 2
EOF

# 6. 测试应用导入
echo "🧪 测试应用..."
sudo -u www-data venv/bin/python -c "
import sys
sys.path.insert(0, '.')
try:
    import app
    print('✅ 应用导入成功')
except Exception as e:
    print(f'❌ 应用导入失败: {e}')
    import traceback
    traceback.print_exc()
"

# 7. 测试数据库连接
echo "🗄️ 测试数据库连接..."
sudo -u www-data venv/bin/python -c "
import sys
sys.path.insert(0, '.')
try:
    from config import get_db_connection
    conn = get_db_connection()
    if conn:
        conn.close()
        print('✅ 数据库连接成功')
    else:
        print('❌ 数据库连接失败')
except Exception as e:
    print(f'❌ 数据库连接错误: {e}')
"

# 8. 导入数据库表（如果需要）
if [ -f "create_tables.sql" ]; then
    echo "🗄️ 导入数据库表..."
    if mysql -u root -pwindows1 pregnancy_management < create_tables.sql 2>/dev/null; then
        echo "✅ 数据库表导入成功"
    else
        echo "⚠️ 数据库表导入失败或已存在"
    fi
else
    echo "⚠️ 未找到create_tables.sql文件"
fi

# 9. 启动/重启服务
echo "🚀 启动应用服务..."
if sudo systemctl is-active --quiet pregnancy-management; then
    echo "🔄 重启现有服务..."
    sudo systemctl restart pregnancy-management
else
    echo "🚀 启动新服务..."
    sudo systemctl start pregnancy-management
fi

sudo systemctl enable pregnancy-management

# 等待服务启动
sleep 3

# 10. 检查服务状态
if sudo systemctl is-active --quiet pregnancy-management; then
    echo "✅ 应用服务运行正常"
else
    echo "❌ 应用服务启动失败"
    echo "查看服务状态:"
    sudo systemctl status pregnancy-management
    echo ""
    echo "查看日志:"
    sudo journalctl -u pregnancy-management --no-pager -n 20
    exit 1
fi

# 11. 测试Web服务
echo "🧪 测试Web服务..."
sleep 2
if curl -s http://127.0.0.1:8000 > /dev/null; then
    echo "✅ Web服务响应正常"
else
    echo "❌ Web服务无响应，检查端口..."
    sudo netstat -tlnp | grep :8000 || echo "端口8000未监听"
fi

echo ""
echo "🎉 快速部署完成！"
echo "=================="
echo "📍 内部地址: http://127.0.0.1:8000"
echo "🌐 外部地址: http://pm.beimoyinhenlinlin.cn/"
echo ""
echo "📋 检查命令："
echo "sudo systemctl status pregnancy-management"
echo "sudo journalctl -u pregnancy-management -f"
echo "curl http://127.0.0.1:8000"
