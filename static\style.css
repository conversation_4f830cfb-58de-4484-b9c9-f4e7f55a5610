/* 全局样式 */
:root {
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --primary-light: #BBDEFB;
    --accent-color: #FF4081;
    --text-primary: #212121;
    --text-secondary: #757575;
    --background-primary: #F5F7FA;
    --background-secondary: #FFFFFF;
    --border-color: #E0E0E0;
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --spacing-unit: 8px;
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --error-color: #F44336;
    --info-color: #2196F3;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}



/* 布局组件 */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-unit) * 2;
    flex: 1;
}

.page-header {
    background: var(--background-secondary);
    padding: calc(var(--spacing-unit) * 3) calc(var(--spacing-unit) * 2);
    margin-bottom: calc(var(--spacing-unit) * 3);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.page-header h1 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: calc(var(--spacing-unit) * 1.5);
}

.page-header h1 i {
    color: var(--primary-color);
}

.page-header .subtitle {
    color: var(--text-secondary);
    margin: calc(var(--spacing-unit) * 1.5) 0 0;
    font-size: 1rem;
}

/* 导航栏 */
.nav {
    background: var(--background-secondary);
    padding: calc(var(--spacing-unit) * 2);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: calc(var(--spacing-unit) * 2);
}

.nav .button {
    background: transparent;
    color: var(--text-primary);
    padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-unit);
    transition: all 0.3s ease;
}

.nav .button:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-dark);
}

/* 卡片组件 */
.card {
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    padding: calc(var(--spacing-unit) * 3);
    box-shadow: var(--shadow);
    margin-bottom: calc(var(--spacing-unit) * 3);
}

/* 表单样式 */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: calc(var(--spacing-unit) * 4);
}

.form-section h2 {
    color: var(--text-primary);
    font-size: 1.4rem;
    margin-bottom: calc(var(--spacing-unit) * 3);
    display: flex;
    align-items: center;
    gap: var(--spacing-unit);
}

.form-section h2 i {
    color: var(--primary-color);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: calc(var(--spacing-unit) * 3);
}

.form-group {
    margin-bottom: calc(var(--spacing-unit) * 2);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-unit);
    color: var(--text-secondary);
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: calc(var(--spacing-unit) * 1.5);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--background-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
    margin: calc(var(--spacing-unit) * 2) 0;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    overflow: hidden;
}

th, td {
    padding: calc(var(--spacing-unit) * 2);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--background-primary);
    color: var(--text-secondary);
    font-weight: 500;
}

tr:hover {
    background: var(--background-primary);
}

/* 按钮样式 */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    gap: var(--spacing-unit);
}

.button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.button.secondary {
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.button.secondary:hover {
    background: var(--primary-light);
}

/* 状态标签 */
.status-tag {
    display: inline-flex;
    align-items: center;
    padding: calc(var(--spacing-unit) * 0.75) calc(var(--spacing-unit) * 1.5);
    border-radius: 16px;
    font-size: 0.875rem;
    font-weight: 500;
    gap: var(--spacing-unit);
}

.status-tag.risk-red {
    background: #FFEBEE;
    color: var(--error-color);
}

.status-tag.risk-yellow {
    background: #FFF8E1;
    color: var(--warning-color);
}

.status-tag.risk-blue {
    background: #E3F2FD;
    color: var(--info-color);
}

.status-tag.vip {
    background: #FFF3E0;
    color: #FF9800;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: var(--spacing-unit);
    }
    
    .page-header {
        padding: calc(var(--spacing-unit) * 2);
        margin-bottom: calc(var(--spacing-unit) * 2);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .nav {
        flex-direction: column;
        align-items: stretch;
    }
    
    .nav .button {
        width: 100%;
        justify-content: center;
    }
    
    .table-container {
        margin: var(--spacing-unit) 0;
    }
    
    th, td {
        padding: var(--spacing-unit);
    }
}

/* 消息提示 */
.alert {
    display: flex;
    align-items: center;
    padding: calc(var(--spacing-unit) * 2);
    border-radius: var(--border-radius);
    margin-bottom: calc(var(--spacing-unit) * 2);
    gap: var(--spacing-unit);
}

.alert.error {
    background: #FFEBEE;
    color: var(--error-color);
}

.alert.success {
    background: #E8F5E9;
    color: var(--success-color);
}

.alert.warning {
    background: #FFF8E1;
    color: var(--warning-color);
}

.alert.info {
    background: #E3F2FD;
    color: var(--info-color);
}

/* 搜索组件 */
.search-section {
    margin-bottom: calc(var(--spacing-unit) * 4);
}

.search-form {
    background: var(--background-secondary);
    padding: calc(var(--spacing-unit) * 3);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.search-input-group {
    display: flex;
    gap: calc(var(--spacing-unit) * 2);
    align-items: center;
}

.search-input-group input {
    flex: 1;
    padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.search-input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.search-button {
    padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-button:hover {
    background: var(--primary-dark);
}

/* 操作按钮 */
.action-button {
    padding: calc(var(--spacing-unit) * 1) calc(var(--spacing-unit) * 2);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-unit);
    margin-right: var(--spacing-unit);
}

.action-button.view {
    background: var(--primary-light);
    color: var(--primary-dark);
}

.action-button.add {
    background: var(--success-color);
    color: white;
}

.action-button.edit {
    background: var(--warning-color);
    color: white;
}

.action-button.delete {
    background: var(--error-color);
    color: white;
}

/* 仪表板样式 */
.dashboard {
    padding: 0 2rem;
    margin-bottom: 2rem;
    flex: 1;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.feature-icon {
    font-size: 2.5em;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: var(--text-primary);
    margin: 0 0 1.5rem;
    font-size: 1.4em;
    font-weight: 500;
}

.feature-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 消息提示样式 */
.messages-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.alert {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background-color: #f44336;
    color: white;
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.alert i {
    font-size: 1.2em;
}

/* 页脚样式 */
.footer {
    background: var(--background-secondary);
    padding: 1.5rem;
    text-align: center;
    color: var(--text-secondary);
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
    margin-top: auto;
}

.footer p {
    margin: 0;
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard {
        padding: 0 1rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .button {
        padding: 0.7rem 1.2rem;
    }
    
    h1 {
        font-size: 1.8em;
    }
    
    .subtitle {
        font-size: 1em;
    }
}

.nav {
    display: flex;
    justify-content: flex-start;
    padding: 1rem 2rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.nav .button {
    padding: 0.6rem 1.2rem;
    font-size: 1em;
    background-color: transparent;
    color: #333;
    border: 1px solid #ddd;
}

.nav .button:hover {
    background-color: #e9ecef;
    border-color: #ddd;
}

.button:hover {
    background-color: #45a049;
}

.alert {
    padding: 10px;
    background-color: #f44336;
    color: white;
    margin-bottom: 15px;
    border-radius: 5px;
}

form {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
}

input[type="text"],
input[type="number"],
input[type="date"],
textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.submit-btn {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.submit-btn:hover {
    background-color: #45a049;
}

/* 搜索表单样式 */
.search-form {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-form .form-group {
    margin-bottom: 1.5rem;
    text-align: center;
}

.search-form input[type="text"] {
    width: 100%;
    max-width: 500px;
    height: 3.6rem;
    padding: 0.8rem 1.2rem;
    font-size: 1.2em;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    margin: 0 auto;
}

.search-form input[type="text"]:hover {
    border-color: #bdbdbd;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.search-form input[type="text"]:focus {
    border-color: #4CAF50;
    background-color: #fff;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
    outline: none;
}

.search-form .button {
    min-width: 160px;
    height: 3.6rem;
    padding: 0 2rem;
    font-size: 1.2em;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    margin: 0 auto;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

.search-form .button:hover {
    background-color: #45a049;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    transform: translateY(-1px);
}

.search-form .button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

.search-form .button i {
    font-size: 1.1em;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .search-form {
        padding: 1.5rem;
        margin: 1rem;
    }

    .search-form input[type="text"] {
        height: 3.4rem;
        font-size: 1.1em;
        padding: 0.6rem 1rem;
    }

    .search-form .button {
        width: 100%;
        max-width: 300px;
        height: 3.4rem;
        font-size: 1.1em;
        padding: 0 1.5rem;
    }
}

.search-results {
    max-width: 1000px;
    margin: 20px auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f5f5f5;
    font-weight: bold;
}

tr:hover {
    background-color: #f9f9f9;
}

.button.small {
    padding: 5px 10px;
    font-size: 14px;
}

select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

.form {
    max-width: 800px;
    margin: 20px auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

.import-container {
    max-width: 800px;
    margin: 20px auto;
    display: flex;
    gap: 20px;
}

.import-instructions {
    flex: 1;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.import-instructions ul {
    padding-left: 20px;
    margin: 10px 0;
}

.import-instructions li {
    margin: 5px 0;
    color: #666;
}

.import-form {
    flex: 1;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

input[type="file"] {
    width: 100%;
    padding: 10px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
}

input[type="file"]:hover {
    border-color: #4CAF50;
}

.patient-info {
    max-width: 800px;
    margin: 20px auto;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    text-align: center;
}

.patient-info p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

.patient-info h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
}

.checkup-records {
    max-width: 1000px;
    margin: 20px auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.notes {
    background-color: #f9f9f9;
    font-style: italic;
}

.no-records {
    text-align: center;
    color: #666;
    margin: 20px 0;
}

textarea {
    resize: vertical;
    min-height: 60px;
}

.detail-card {
    max-width: 1000px;
    margin: 20px auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.detail-section {
    margin-bottom: 30px;
}

.detail-section h3 {
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-item label {
    font-weight: bold;
    color: #666;
    margin-bottom: 5px;
}

.detail-item span {
    color: #333;
}

.notes-box {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    white-space: pre-wrap;
}

.risk-color {
    padding: 2px 8px;
    border-radius: 3px;
    font-weight: bold;
}

.risk-color.红 {
    background-color: #ffebee;
    color: #c62828;
}

.risk-color.黄 {
    background-color: #fff3e0;
    color: #ef6c00;
}

.risk-color.蓝 {
    background-color: #e3f2fd;
    color: #1565c0;
}

.record-row {
    cursor: pointer;
}

.record-row:hover {
    background-color: #f5f5f5;
}

.edit-hint {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.editable {
    cursor: pointer;
    position: relative;
}

.editable:hover {
    background-color: #f5f5f5;
}

.editable:hover::after {
    content: '双击编辑';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: #999;
}

.edit-input {
    width: 100%;
    padding: 5px;
    border: 1px solid #333;
    border-radius: 4px;
    font-size: inherit;
}

.edit-input:focus {
    outline: none;
    border-color: #000;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.appointments-container {
    max-width: 1200px;
    margin: 20px auto;
}

.appointment-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.appointment-section h2 {
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.count {
    font-size: 14px;
    color: #666;
    margin-left: 10px;
}

.tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    margin: 0 2px;
}

.tag.high-risk {
    background-color: #ffebee;
    color: #c62828;
}

.tag.vip {
    background-color: #fff8e1;
    color: #ffa000;
}

tr.high-risk {
    background-color: #fff5f5;
}

tr.vip {
    background-color: #fffdf5;
}

.no-data {
    text-align: center;
    color: #666;
    padding: 20px;
}

.overdue h2 {
    color: #c62828;
}

.upcoming h2 {
    color: #2e7d32;
}

/* 导出按钮样式 */
.button.export {
    background-color: #4CAF50;
}

.button.export:hover {
    background-color: #45a049;
}

/* 产检记录详情页面紧凑样式 */
.detail-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
}

/* 表单卡片基础样式 */
.detail-card {
    background: white;
    border-radius: 8px;
    padding: 1.2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

/* 卡片标题样式 */
.detail-card h2 {
    color: #333;
    font-size: 1.2em;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

/* 表单网格布局 */
.detail-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

/* 表单项样式 */
.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.info-item label {
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

.info-item .value {
    color: #333;
    font-size: 1em;
    font-weight: 500;
    padding: 0.3rem 0;
}

/* 风险评估区域样式 */
.risk-assessment {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 0.5rem;
}

.risk-tag {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
    margin-right: 0.5rem;
}

.risk-tag.red {
    background-color: #ffebee;
    color: #c62828;
}

.risk-tag.yellow {
    background-color: #fff3e0;
    color: #ef6c00;
}

.risk-tag.blue {
    background-color: #e3f2fd;
    color: #1565c0;
}

/* 备注区域样式 */
.notes-section {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.notes-section h3 {
    color: #555;
    font-size: 1em;
    margin-bottom: 0.5rem;
}

.notes-content {
    color: #666;
    font-size: 0.95em;
    line-height: 1.5;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .detail-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .detail-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .risk-assessment {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .detail-container {
        padding: 0.8rem;
    }
    
    .detail-card {
        padding: 1rem;
    }
    
    .detail-grid {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }
    
    .info-item label {
        font-size: 0.85em;
    }
    
    .info-item .value {
        font-size: 0.95em;
    }
}

/* 添加孕产妇页面特定样式 */
.management-card {
    margin-top: 1.5rem;
}

.detail-form select {
    height: 2.4rem;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23666' d='M0 2l4 4 4-4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem center;
    background-size: 8px 8px;
    padding-right: 2rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.detail-form select:focus {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%234CAF50' d='M0 2l4 4 4-4z'/%3E%3C/svg%3E");
}

.detail-form input[type="date"] {
    height: 2.4rem;
}

.detail-form input[type="number"] {
    height: 2.4rem;
}

/* 响应式设计补充 */
@media (max-width: 768px) {
    .management-card {
        margin-top: 1rem;
    }
    
    .detail-form select,
    .detail-form input[type="date"],
    .detail-form input[type="number"] {
        height: 2.8rem; /* 移动端更大的点击区域 */
    }
}

/* 风险评估特殊样式 */
#risk_color {
    height: 4rem;
    font-size: 1.2em;
    padding: 0.8rem 2.5rem 0.8rem 1.2rem;
    background-position: right 1.2rem center;
    background-size: 12px 12px;
}

#risk_color option {
    padding: 0.8rem;
    font-size: 1.1em;
}

/* 高危和VIP选择框样式 */
.risk-vip-group select {
    height: 3.6rem;
    font-size: 1.2em;
    padding: 0.8rem 2.5rem 0.8rem 1.2rem;
    background-position: right 1.2rem center;
    background-size: 12px 12px;
}

.risk-vip-group select option {
    padding: 0.8rem;
    font-size: 1.1em;
}

.risk-vip-group {
    flex: 1;
    min-width: 200px;
}

/* 管理信息卡片布局调整 */
.management-card .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
    .risk-vip-group select {
        height: 3.2rem;
        font-size: 1.1em;
    }
    
    .management-card .info-grid {
        grid-template-columns: 1fr;
    }
}

/* 基本信息录入样式 */
.detail-card .info-grid {
    gap: 2rem 3rem;
}

.detail-card input {
    height: 3.6rem;
    font-size: 1.15em;
    padding: 0.8rem 1.2rem;
}

/* 特定输入框样式 */
#name, #outpatient_number, #id_card {
    font-size: 1.2em;
    height: 3.8rem;
    padding: 0.8rem 1.5rem;
}

#age {
    text-align: center;
    font-size: 1.2em;
    height: 3.8rem;
}

#phone {
    letter-spacing: 1px;
    font-size: 1.2em;
    height: 3.8rem;
}

/* 日期输入框统一样式 */
.detail-form input[type="date"] {
    height: 3.6rem;
    padding: 0.8rem 1.2rem;
    font-size: 1.15em;
}

/* 数字输入框统一样式 */
.detail-form input[type="number"] {
    height: 3.6rem;
    padding: 0.8rem 1.2rem;
    font-size: 1.15em;
    text-align: center;
}

/* 标签样式优化 */
.detail-card .info-item label {
    font-size: 1.1em;
    margin-bottom: 0.4rem;
    color: #444;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .detail-card .info-grid {
        gap: 1.5rem;
    }
    
    .detail-card input,
    .detail-form input[type="date"],
    .detail-form input[type="number"],
    #name, #outpatient_number, #id_card, #age, #phone {
        height: 3.4rem;
        font-size: 1.1em;
        padding: 0.6rem 1rem;
    }
    
    .detail-card .info-item label {
        font-size: 1em;
        margin-bottom: 0.3rem;
    }
}

/* 预约表格状态列样式 */
.appointments-container table tr.high-risk {
    background-color: rgba(255, 235, 238, 0.3);
}

.appointments-container table tr.vip {
    border-left: 4px solid #ffa000;
}

.appointments-container table td {
    padding: 1rem;
    vertical-align: middle;
}

/* 状态标签样式 */
.risk-color {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.95em;
}

.risk-color.红 {
    background-color: #ffebee;
    color: #c62828;
}

.risk-color.黄 {
    background-color: #fff3e0;
    color: #ef6c00;
}

.risk-color.蓝 {
    background-color: #e3f2fd;
    color: #1565c0;
}

/* 表格操作按钮样式 */
.appointments-container .button.small {
    padding: 0.4rem 0.8rem;
    font-size: 0.9em;
    margin: 0 0.3rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .appointments-container table td {
        padding: 0.8rem;
    }
    
    .risk-color {
        padding: 0.2rem 0.6rem;
        font-size: 0.9em;
    }
}

/* 孕产妇操作按钮组 */
.patient-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
    width: 100%;
}

.patient-actions .button {
    min-width: 160px;
    padding: 0.8rem 1.5rem;
    font-size: 1.1em;
    text-align: center;
}

.button.delete {
    background-color: #dc3545;
    color: white;
    border: none;
    cursor: pointer;
    min-width: 160px;
    padding: 0.8rem 1.5rem;
    font-size: 1.1em;
}

.button.delete:hover {
    background-color: #c82333;
}

@media (max-width: 768px) {
    .patient-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .patient-actions .button,
    .button.delete {
        width: 80%;
        max-width: 300px;
    }
}

/* 标题样式 */
h1 {
    text-align: center;
    color: #333;
    margin: 2rem 0;
    font-size: 2em;
}

/* 分隔线 */
.patient-actions {
    position: relative;
    padding-top: 1rem;
}

.patient-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 800px;
    height: 1px;
    background-color: #e9ecef;
}

@media (max-width: 768px) {
    .nav {
        padding: 1rem;
    }
    
    .nav .button {
        padding: 0.5rem 1rem;
        font-size: 0.9em;
    }
    
    h1 {
        font-size: 1.6em;
        margin: 1.5rem 0;
    }
}

/* 添加产检记录页面样式 */
.form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* 孕产妇信息摘要样式 */
.patient-summary {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.patient-summary h2 {
    color: #333;
    font-size: 1.3em;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.patient-summary h2 i {
    color: #4CAF50;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.summary-item label {
    color: #666;
    font-size: 0.9em;
}

.summary-item span {
    color: #333;
    font-size: 1.1em;
    font-weight: 500;
}

/* 表单样式优化 */
.checkup-form {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 1rem;
}

.form-section h2 {
    color: #333;
    font-size: 1.3em;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section h2 i {
    color: #4CAF50;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 0;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    color: #555;
    font-size: 1em;
    margin-bottom: 0.5rem;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group select {
    width: 100%;
    height: 3.6rem;
    padding: 0.8rem 1.2rem;
    font-size: 1.1em;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    color: #333;
}

.form-group input:hover,
.form-group select:hover {
    border-color: #bdbdbd;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.form-group input:focus,
.form-group select:focus {
    border-color: #4CAF50;
    background-color: #fff;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
    outline: none;
}

/* 特定字段样式 */
#name, #outpatient_number, #id_card {
    font-size: 1.2em;
    height: 3.8rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

#age {
    text-align: center;
    font-size: 1.3em;
    height: 3.8rem;
    font-weight: 600;
    letter-spacing: 1px;
}

#phone {
    letter-spacing: 1.5px;
    font-size: 1.2em;
    height: 3.8rem;
    font-weight: 500;
}

/* 表单标签样式 */
.form-group label {
    display: block;
    color: #444;
    font-size: 1.05em;
    margin-bottom: 0.6rem;
    font-weight: 500;
    transition: color 0.3s ease;
    padding-left: 0.3rem;
}

.form-group:focus-within label {
    color: #4CAF50;
}

/* 下拉选择框样式 */
.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M2 4l4 4 4-4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1.2rem center;
    background-size: 12px 12px;
    padding-right: 3rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-group select:focus {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%234CAF50' d='M2 4l4 4 4-4z'/%3E%3C/svg%3E");
}

/* 高危和VIP选择框特殊样式 */
#is_high_risk, #is_vip {
    height: 3.8rem;
    font-size: 1.15em;
    font-weight: 500;
    text-align: center;
    padding-left: 2rem;
}

/* 日期输入框特殊样式 */
input[type="date"] {
    position: relative;
    padding-right: 2rem;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    filter: invert(60%);
    opacity: 0.7;
    transition: opacity 0.3s ease;
    padding: 0.5rem;
    border-radius: 8px;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
    background-color: rgba(0,0,0,0.05);
}

/* 数字输入框特殊样式 */
input[type="number"] {
    position: relative;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    opacity: 1;
    height: 2.8rem;
    margin: 0;
    cursor: pointer;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
}

/* 表单分组标题样式优化 */
.form-section h2 {
    color: #2c3e50;
    font-size: 1.4em;
    margin-bottom: 1.8rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid #e0e0e0;
}

.form-section h2 i {
    color: #4CAF50;
    font-size: 1.2em;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-group input[type="text"],
    .form-group input[type="number"],
    .form-group input[type="date"],
    .form-group select {
        height: 3.4rem;
        font-size: 16px;
        padding: 0.6rem 1rem;
        border-radius: 10px;
    }
    
    .form-section {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .submit-btn {
        border-radius: 10px;
    }
}

/* 添加新孕产妇页面样式 */
.patient-form {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 1rem;
}

.form-section h2 {
    color: #333;
    font-size: 1.3em;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section h2 i {
    color: #4CAF50;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    color: #555;
    font-size: 1em;
    margin-bottom: 0.5rem;
}

/* 基本信息输入框样式 */
.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group select {
    width: 100%;
    height: 3.6rem;
    padding: 0.8rem 1.2rem;
    font-size: 1.1em;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    color: #333;
}

.form-group input:hover,
.form-group select:hover {
    border-color: #bdbdbd;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    color: #333;
}

.form-group input:hover,
.form-group select:hover {
    background-color: #fff;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.form-group input:focus,
.form-group select:focus {
    height: 3.8rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    background-color: #fff;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
    outline: none;
}

/* 下拉选择框样式 */
.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23666' d='M0 2l4 4 4-4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1.2rem center;
    background-size: 10px 10px;
    padding-right: 2.5rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* 高危和VIP选择框特殊样式 */
#is_high_risk, #is_vip {
    height: 3.4rem;
    font-size: 1.15em;
    font-weight: 500;
}

/* 提交按钮样式 */
.form-actions {
    margin-top: 2.5rem;
    text-align: center;
}

.submit-btn {
    min-width: 200px;
    padding: 1rem 2.5rem;
    font-size: 1.2em;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    transform: translateY(-1px);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: background-color 0.3s ease;
}

.submit-btn:hover {
    background-color: #45a049;
}

.submit-btn i {
    font-size: 1.1em;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .form-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.2rem;
    }
}

@media (max-width: 992px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .form-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1.2rem;
    }
}

@media (max-width: 768px) {
    .form-container {
        padding: 0.5rem;
    }
    
    .patient-form {
        padding: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-section h2 {
        font-size: 1.2em;
        margin-bottom: 1rem;
    }
    
    .form-group label {
        font-size: 0.95em;
    }
    
    .form-group input[type="text"],
    .form-group input[type="number"],
    .form-group input[type="date"],
    .form-group select {
        height: 3rem;
        font-size: 16px;
        padding: 0.6rem 1rem;
    }
    
    #name, #outpatient_number, #id_card, #age, #phone {
        height: 3rem;
        font-size: 16px;
    }
    
    .submit-btn {
        width: 100%;
        max-width: 300px;
        padding: 0.8rem 1.5rem;
    }
} 